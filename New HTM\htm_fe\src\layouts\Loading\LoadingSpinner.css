/* Loading spinner container */
.spinner-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh; /* Full page height */
  }
  
  /* Spinner styles */
  .spinner {
    width: 50px;
    height: 50px;
    border: 6px solid rgba(0, 0, 0, 0.2); /* Light gray border */
    border-top: 6px solid #333; /* Dark gray for the "active" part */
    border-radius: 50%; /* Make it circular */
    animation: spin 1s linear infinite; /* Rotate infinitely */
  }
  
  /* Keyframes for spin animation */
  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  