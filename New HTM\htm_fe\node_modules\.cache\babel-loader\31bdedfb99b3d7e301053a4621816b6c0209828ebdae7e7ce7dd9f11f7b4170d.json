{"ast": null, "code": "// Authentication Redux slice\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\n// Initial state\nconst initialState = {\n  user: null,\n  profile: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  accessToken: localStorage.getItem('accessToken'),\n  refreshToken: localStorage.getItem('refreshToken'),\n  tokenExpiry: null\n};\n\n// Async thunks (will be implemented with actual API calls later)\nexport const loginUser = createAsyncThunk('auth/loginUser', async (credentials, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/auth/login', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(credentials)\n    });\n    if (!response.ok) {\n      throw new Error('Login failed');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const refreshAccessToken = createAsyncThunk('auth/refreshAccessToken', async (_, {\n  getState,\n  rejectWithValue\n}) => {\n  try {\n    const state = getState();\n    const refreshToken = state.auth.refreshToken;\n    if (!refreshToken) {\n      throw new Error('No refresh token available');\n    }\n\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/auth/refresh', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify({\n        refreshToken\n      })\n    });\n    if (!response.ok) {\n      throw new Error('Token refresh failed');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const logoutUser = createAsyncThunk('auth/logoutUser', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    await fetch('/api/auth/logout', {\n      method: 'POST',\n      credentials: 'include'\n    });\n\n    // Clear local storage\n    localStorage.removeItem('accessToken');\n    localStorage.removeItem('refreshToken');\n    return null;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n\n// Auth slice\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    // Synchronous actions\n    setUser: (state, action) => {\n      state.user = action.payload;\n      state.isAuthenticated = !!action.payload;\n    },\n    setProfile: (state, action) => {\n      state.profile = action.payload;\n    },\n    setTokens: (state, action) => {\n      state.accessToken = action.payload.accessToken;\n      state.refreshToken = action.payload.refreshToken;\n      state.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;\n\n      // Store in localStorage\n      localStorage.setItem('accessToken', action.payload.accessToken);\n      localStorage.setItem('refreshToken', action.payload.refreshToken);\n    },\n    clearAuth: state => {\n      state.user = null;\n      state.profile = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      state.error = null;\n\n      // Clear localStorage\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n    },\n    clearError: state => {\n      state.error = null;\n    }\n  },\n  extraReducers: builder => {\n    // Login user\n    builder.addCase(loginUser.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(loginUser.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.user = action.payload.user;\n      state.isAuthenticated = true;\n      state.accessToken = action.payload.accessToken;\n      state.refreshToken = action.payload.refreshToken;\n      state.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;\n    }).addCase(loginUser.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    });\n\n    // Refresh token\n    builder.addCase(refreshAccessToken.fulfilled, (state, action) => {\n      state.accessToken = action.payload.accessToken;\n      state.tokenExpiry = Date.now() + action.payload.expiresIn * 1000;\n      localStorage.setItem('accessToken', action.payload.accessToken);\n    }).addCase(refreshAccessToken.rejected, state => {\n      // If refresh fails, clear auth state\n      state.user = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n    });\n\n    // Logout user\n    builder.addCase(logoutUser.fulfilled, state => {\n      state.user = null;\n      state.profile = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      state.error = null;\n    });\n  }\n});\nexport const {\n  setUser,\n  setProfile,\n  setTokens,\n  clearAuth,\n  clearError\n} = authSlice.actions;\nexport default authSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "initialState", "user", "profile", "isAuthenticated", "isLoading", "error", "accessToken", "localStorage", "getItem", "refreshToken", "tokenExpiry", "loginUser", "credentials", "rejectWithValue", "response", "fetch", "method", "headers", "body", "JSON", "stringify", "ok", "Error", "data", "json", "message", "refreshAccessToken", "_", "getState", "state", "auth", "logoutUser", "removeItem", "authSlice", "name", "reducers", "setUser", "action", "payload", "setProfile", "setTokens", "Date", "now", "expiresIn", "setItem", "clearAuth", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/authSlice.ts"], "sourcesContent": ["// Authentication Redux slice\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { AuthState, AuthUser, UserProfile } from '../../../shared/types';\n\n// Initial state\nconst initialState: AuthState = {\n  user: null,\n  profile: null,\n  isAuthenticated: false,\n  isLoading: false,\n  error: null,\n  accessToken: localStorage.getItem('accessToken'),\n  refreshToken: localStorage.getItem('refreshToken'),\n  tokenExpiry: null,\n};\n\n// Async thunks (will be implemented with actual API calls later)\nexport const loginUser = createAsyncThunk(\n  'auth/loginUser',\n  async (credentials: { email: string; password: string }, { rejectWithValue }) => {\n    try {\n      // TODO: Replace with actual API call\n      const response = await fetch('/api/auth/login', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(credentials),\n      });\n      \n      if (!response.ok) {\n        throw new Error('Login failed');\n      }\n      \n      const data = await response.json();\n      return data;\n    } catch (error: any) {\n      return rejectWithValue(error.message);\n    }\n  }\n);\n\nexport const refreshAccessToken = createAsyncThunk(\n  'auth/refreshAccessToken',\n  async (_, { getState, rejectWithValue }) => {\n    try {\n      const state = getState() as { auth: AuthState };\n      const refreshToken = state.auth.refreshToken;\n      \n      if (!refreshToken) {\n        throw new Error('No refresh token available');\n      }\n      \n      // TODO: Replace with actual API call\n      const response = await fetch('/api/auth/refresh', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify({ refreshToken }),\n      });\n      \n      if (!response.ok) {\n        throw new Error('Token refresh failed');\n      }\n      \n      const data = await response.json();\n      return data;\n    } catch (error: any) {\n      return rejectWithValue(error.message);\n    }\n  }\n);\n\nexport const logoutUser = createAsyncThunk(\n  'auth/logoutUser',\n  async (_, { rejectWithValue }) => {\n    try {\n      // TODO: Replace with actual API call\n      await fetch('/api/auth/logout', {\n        method: 'POST',\n        credentials: 'include',\n      });\n      \n      // Clear local storage\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n      \n      return null;\n    } catch (error: any) {\n      return rejectWithValue(error.message);\n    }\n  }\n);\n\n// Auth slice\nconst authSlice = createSlice({\n  name: 'auth',\n  initialState,\n  reducers: {\n    // Synchronous actions\n    setUser: (state, action: PayloadAction<AuthUser | null>) => {\n      state.user = action.payload;\n      state.isAuthenticated = !!action.payload;\n    },\n    \n    setProfile: (state, action: PayloadAction<UserProfile | null>) => {\n      state.profile = action.payload;\n    },\n    \n    setTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string; expiresIn: number }>) => {\n      state.accessToken = action.payload.accessToken;\n      state.refreshToken = action.payload.refreshToken;\n      state.tokenExpiry = Date.now() + (action.payload.expiresIn * 1000);\n      \n      // Store in localStorage\n      localStorage.setItem('accessToken', action.payload.accessToken);\n      localStorage.setItem('refreshToken', action.payload.refreshToken);\n    },\n    \n    clearAuth: (state) => {\n      state.user = null;\n      state.profile = null;\n      state.isAuthenticated = false;\n      state.accessToken = null;\n      state.refreshToken = null;\n      state.tokenExpiry = null;\n      state.error = null;\n      \n      // Clear localStorage\n      localStorage.removeItem('accessToken');\n      localStorage.removeItem('refreshToken');\n    },\n    \n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n  \n  extraReducers: (builder) => {\n    // Login user\n    builder\n      .addCase(loginUser.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(loginUser.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.user = action.payload.user;\n        state.isAuthenticated = true;\n        state.accessToken = action.payload.accessToken;\n        state.refreshToken = action.payload.refreshToken;\n        state.tokenExpiry = Date.now() + (action.payload.expiresIn * 1000);\n      })\n      .addCase(loginUser.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      });\n    \n    // Refresh token\n    builder\n      .addCase(refreshAccessToken.fulfilled, (state, action) => {\n        state.accessToken = action.payload.accessToken;\n        state.tokenExpiry = Date.now() + (action.payload.expiresIn * 1000);\n        localStorage.setItem('accessToken', action.payload.accessToken);\n      })\n      .addCase(refreshAccessToken.rejected, (state) => {\n        // If refresh fails, clear auth state\n        state.user = null;\n        state.isAuthenticated = false;\n        state.accessToken = null;\n        state.refreshToken = null;\n        state.tokenExpiry = null;\n        localStorage.removeItem('accessToken');\n        localStorage.removeItem('refreshToken');\n      });\n    \n    // Logout user\n    builder\n      .addCase(logoutUser.fulfilled, (state) => {\n        state.user = null;\n        state.profile = null;\n        state.isAuthenticated = false;\n        state.accessToken = null;\n        state.refreshToken = null;\n        state.tokenExpiry = null;\n        state.error = null;\n      });\n  },\n});\n\nexport const { setUser, setProfile, setTokens, clearAuth, clearError } = authSlice.actions;\nexport default authSlice.reducer;\n"], "mappings": "AAAA;AACA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAG/E;AACA,MAAMC,YAAuB,GAAG;EAC9BC,IAAI,EAAE,IAAI;EACVC,OAAO,EAAE,IAAI;EACbC,eAAe,EAAE,KAAK;EACtBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,WAAW,EAAEC,YAAY,CAACC,OAAO,CAAC,aAAa,CAAC;EAChDC,YAAY,EAAEF,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;EAClDE,WAAW,EAAE;AACf,CAAC;;AAED;AACA,OAAO,MAAMC,SAAS,GAAGZ,gBAAgB,CACvC,gBAAgB,EAChB,OAAOa,WAAgD,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/E,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,iBAAiB,EAAE;MAC9CC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACR,WAAW;IAClC,CAAC,CAAC;IAEF,IAAI,CAACE,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;IACjC;IAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOlB,KAAU,EAAE;IACnB,OAAOQ,eAAe,CAACR,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMC,kBAAkB,GAAG3B,gBAAgB,CAChD,yBAAyB,EACzB,OAAO4B,CAAC,EAAE;EAAEC,QAAQ;EAAEf;AAAgB,CAAC,KAAK;EAC1C,IAAI;IACF,MAAMgB,KAAK,GAAGD,QAAQ,CAAC,CAAwB;IAC/C,MAAMnB,YAAY,GAAGoB,KAAK,CAACC,IAAI,CAACrB,YAAY;IAE5C,IAAI,CAACA,YAAY,EAAE;MACjB,MAAM,IAAIa,KAAK,CAAC,4BAA4B,CAAC;IAC/C;;IAEA;IACA,MAAMR,QAAQ,GAAG,MAAMC,KAAK,CAAC,mBAAmB,EAAE;MAChDC,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QAAEX;MAAa,CAAC;IACvC,CAAC,CAAC;IAEF,IAAI,CAACK,QAAQ,CAACO,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,sBAAsB,CAAC;IACzC;IAEA,MAAMC,IAAI,GAAG,MAAMT,QAAQ,CAACU,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOlB,KAAU,EAAE;IACnB,OAAOQ,eAAe,CAACR,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMM,UAAU,GAAGhC,gBAAgB,CACxC,iBAAiB,EACjB,OAAO4B,CAAC,EAAE;EAAEd;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF;IACA,MAAME,KAAK,CAAC,kBAAkB,EAAE;MAC9BC,MAAM,EAAE,MAAM;MACdJ,WAAW,EAAE;IACf,CAAC,CAAC;;IAEF;IACAL,YAAY,CAACyB,UAAU,CAAC,aAAa,CAAC;IACtCzB,YAAY,CAACyB,UAAU,CAAC,cAAc,CAAC;IAEvC,OAAO,IAAI;EACb,CAAC,CAAC,OAAO3B,KAAU,EAAE;IACnB,OAAOQ,eAAe,CAACR,KAAK,CAACoB,OAAO,CAAC;EACvC;AACF,CACF,CAAC;;AAED;AACA,MAAMQ,SAAS,GAAGnC,WAAW,CAAC;EAC5BoC,IAAI,EAAE,MAAM;EACZlC,YAAY;EACZmC,QAAQ,EAAE;IACR;IACAC,OAAO,EAAEA,CAACP,KAAK,EAAEQ,MAAsC,KAAK;MAC1DR,KAAK,CAAC5B,IAAI,GAAGoC,MAAM,CAACC,OAAO;MAC3BT,KAAK,CAAC1B,eAAe,GAAG,CAAC,CAACkC,MAAM,CAACC,OAAO;IAC1C,CAAC;IAEDC,UAAU,EAAEA,CAACV,KAAK,EAAEQ,MAAyC,KAAK;MAChER,KAAK,CAAC3B,OAAO,GAAGmC,MAAM,CAACC,OAAO;IAChC,CAAC;IAEDE,SAAS,EAAEA,CAACX,KAAK,EAAEQ,MAAuF,KAAK;MAC7GR,KAAK,CAACvB,WAAW,GAAG+B,MAAM,CAACC,OAAO,CAAChC,WAAW;MAC9CuB,KAAK,CAACpB,YAAY,GAAG4B,MAAM,CAACC,OAAO,CAAC7B,YAAY;MAChDoB,KAAK,CAACnB,WAAW,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIL,MAAM,CAACC,OAAO,CAACK,SAAS,GAAG,IAAK;;MAElE;MACApC,YAAY,CAACqC,OAAO,CAAC,aAAa,EAAEP,MAAM,CAACC,OAAO,CAAChC,WAAW,CAAC;MAC/DC,YAAY,CAACqC,OAAO,CAAC,cAAc,EAAEP,MAAM,CAACC,OAAO,CAAC7B,YAAY,CAAC;IACnE,CAAC;IAEDoC,SAAS,EAAGhB,KAAK,IAAK;MACpBA,KAAK,CAAC5B,IAAI,GAAG,IAAI;MACjB4B,KAAK,CAAC3B,OAAO,GAAG,IAAI;MACpB2B,KAAK,CAAC1B,eAAe,GAAG,KAAK;MAC7B0B,KAAK,CAACvB,WAAW,GAAG,IAAI;MACxBuB,KAAK,CAACpB,YAAY,GAAG,IAAI;MACzBoB,KAAK,CAACnB,WAAW,GAAG,IAAI;MACxBmB,KAAK,CAACxB,KAAK,GAAG,IAAI;;MAElB;MACAE,YAAY,CAACyB,UAAU,CAAC,aAAa,CAAC;MACtCzB,YAAY,CAACyB,UAAU,CAAC,cAAc,CAAC;IACzC,CAAC;IAEDc,UAAU,EAAGjB,KAAK,IAAK;MACrBA,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EAED0C,aAAa,EAAGC,OAAO,IAAK;IAC1B;IACAA,OAAO,CACJC,OAAO,CAACtC,SAAS,CAACuC,OAAO,EAAGrB,KAAK,IAAK;MACrCA,KAAK,CAACzB,SAAS,GAAG,IAAI;MACtByB,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACD4C,OAAO,CAACtC,SAAS,CAACwC,SAAS,EAAE,CAACtB,KAAK,EAAEQ,MAAM,KAAK;MAC/CR,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAAC5B,IAAI,GAAGoC,MAAM,CAACC,OAAO,CAACrC,IAAI;MAChC4B,KAAK,CAAC1B,eAAe,GAAG,IAAI;MAC5B0B,KAAK,CAACvB,WAAW,GAAG+B,MAAM,CAACC,OAAO,CAAChC,WAAW;MAC9CuB,KAAK,CAACpB,YAAY,GAAG4B,MAAM,CAACC,OAAO,CAAC7B,YAAY;MAChDoB,KAAK,CAACnB,WAAW,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIL,MAAM,CAACC,OAAO,CAACK,SAAS,GAAG,IAAK;IACpE,CAAC,CAAC,CACDM,OAAO,CAACtC,SAAS,CAACyC,QAAQ,EAAE,CAACvB,KAAK,EAAEQ,MAAM,KAAK;MAC9CR,KAAK,CAACzB,SAAS,GAAG,KAAK;MACvByB,KAAK,CAACxB,KAAK,GAAGgC,MAAM,CAACC,OAAiB;IACxC,CAAC,CAAC;;IAEJ;IACAU,OAAO,CACJC,OAAO,CAACvB,kBAAkB,CAACyB,SAAS,EAAE,CAACtB,KAAK,EAAEQ,MAAM,KAAK;MACxDR,KAAK,CAACvB,WAAW,GAAG+B,MAAM,CAACC,OAAO,CAAChC,WAAW;MAC9CuB,KAAK,CAACnB,WAAW,GAAG+B,IAAI,CAACC,GAAG,CAAC,CAAC,GAAIL,MAAM,CAACC,OAAO,CAACK,SAAS,GAAG,IAAK;MAClEpC,YAAY,CAACqC,OAAO,CAAC,aAAa,EAAEP,MAAM,CAACC,OAAO,CAAChC,WAAW,CAAC;IACjE,CAAC,CAAC,CACD2C,OAAO,CAACvB,kBAAkB,CAAC0B,QAAQ,EAAGvB,KAAK,IAAK;MAC/C;MACAA,KAAK,CAAC5B,IAAI,GAAG,IAAI;MACjB4B,KAAK,CAAC1B,eAAe,GAAG,KAAK;MAC7B0B,KAAK,CAACvB,WAAW,GAAG,IAAI;MACxBuB,KAAK,CAACpB,YAAY,GAAG,IAAI;MACzBoB,KAAK,CAACnB,WAAW,GAAG,IAAI;MACxBH,YAAY,CAACyB,UAAU,CAAC,aAAa,CAAC;MACtCzB,YAAY,CAACyB,UAAU,CAAC,cAAc,CAAC;IACzC,CAAC,CAAC;;IAEJ;IACAgB,OAAO,CACJC,OAAO,CAAClB,UAAU,CAACoB,SAAS,EAAGtB,KAAK,IAAK;MACxCA,KAAK,CAAC5B,IAAI,GAAG,IAAI;MACjB4B,KAAK,CAAC3B,OAAO,GAAG,IAAI;MACpB2B,KAAK,CAAC1B,eAAe,GAAG,KAAK;MAC7B0B,KAAK,CAACvB,WAAW,GAAG,IAAI;MACxBuB,KAAK,CAACpB,YAAY,GAAG,IAAI;MACzBoB,KAAK,CAACnB,WAAW,GAAG,IAAI;MACxBmB,KAAK,CAACxB,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAE+B,OAAO;EAAEG,UAAU;EAAEC,SAAS;EAAEK,SAAS;EAAEC;AAAW,CAAC,GAAGb,SAAS,CAACoB,OAAO;AAC1F,eAAepB,SAAS,CAACqB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}