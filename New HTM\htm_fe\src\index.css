@tailwind base;
@tailwind components;
@tailwind utilities;


body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

/* Animation cho chữ chào mừng (<PERSON><PERSON> khi <PERSON>nh bắt đầu) */
@keyframes welcomeFade {
  0% {
    opacity: 1;
  }
  10% {
    opacity: 1;
  }
  20% {
    opacity: 0;
  }
  100% {
    opacity: 0;
  }
}

/* Animation cho ảnh */
@keyframes imageSlide {
  0% {
    transform: translateX(-100%);
    visibility: visible;
    z-index: 1;
  }
  33.33% {
    transform: translateX(0);
    visibility: visible;
    z-index: 1;
  }
  66.66% {
    transform: translateX(0);
    visibility: visible;
    z-index: 1;
  }
  99.99% {
    transform: translateX(100%);
    visibility: visible;
    z-index: 1;
  }
  100% {
    transform: translateX(100%);
    visibility: hidden;
    z-index: 0;
  }
}

/* Áp dụng animation */
.animate-welcomeFade {
  animation: welcomeFade 6s infinite ease-in-out;
}

.animate-imageSlide {
  animation: imageSlide 6s infinite ease-in-out;
}

.flash-correct {
  animation: flashGreen 3s ease-in-out !important;
  background-color: rgba(34, 197, 94, 0.2) !important;
}

.flash-incorrect {
  animation: flashRed 3s ease-in-out !important;
  background-color: rgba(239, 68, 68, 0.2) !important;
}

@keyframes flashGreen {
  0% { background-color: rgba(34, 197, 94, 0.6) !important; }
  50% { background-color: rgba(34, 197, 94, 0.4) !important; }
  100% { background-color: rgba(51, 65, 85, 0.6) !important; }
}

@keyframes flashRed {
  0% { background-color: rgba(239, 68, 68, 0.6) !important; }
  50% { background-color: rgba(239, 68, 68, 0.4) !important; }
  100% { background-color: rgba(51, 65, 85, 0.6) !important; }
}


@keyframes shrink {
  from {
    width: 100%;
  }
  to {
    width: 0%;
  }
}
.player-item {
  position: relative;
}
