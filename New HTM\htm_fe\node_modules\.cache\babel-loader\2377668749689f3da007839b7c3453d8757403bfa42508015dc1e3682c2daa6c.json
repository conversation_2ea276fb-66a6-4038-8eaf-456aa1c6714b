{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\app\\\\store\\\\providers\\\\ReduxProvider.tsx\";\n// Redux Provider component\nimport React from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from '../index';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nexport const ReduxProvider = ({\n  children\n}) => {\n  return /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: children\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 10\n  }, this);\n};\n_c = ReduxProvider;\nexport default ReduxProvider;\nvar _c;\n$RefreshReg$(_c, \"ReduxProvider\");", "map": {"version": 3, "names": ["React", "Provider", "store", "jsxDEV", "_jsxDEV", "ReduxProvider", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/providers/ReduxProvider.tsx"], "sourcesContent": ["// Redux Provider component\nimport React, { ReactNode } from 'react';\nimport { Provider } from 'react-redux';\nimport { store } from '../index';\n\ninterface ReduxProviderProps {\n  children: ReactNode;\n}\n\nexport const ReduxProvider: React.FC<ReduxProviderProps> = ({ children }) => {\n  return <Provider store={store}>{children}</Provider>;\n};\n\nexport default ReduxProvider;\n"], "mappings": ";AAAA;AACA,OAAOA,KAAK,MAAqB,OAAO;AACxC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,KAAK,QAAQ,UAAU;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAMjC,OAAO,MAAMC,aAA2C,GAAGA,CAAC;EAAEC;AAAS,CAAC,KAAK;EAC3E,oBAAOF,OAAA,CAACH,QAAQ;IAACC,KAAK,EAAEA,KAAM;IAAAI,QAAA,EAAEA;EAAQ;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAW,CAAC;AACtD,CAAC;AAACC,EAAA,GAFWN,aAA2C;AAIxD,eAAeA,aAAa;AAAC,IAAAM,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}