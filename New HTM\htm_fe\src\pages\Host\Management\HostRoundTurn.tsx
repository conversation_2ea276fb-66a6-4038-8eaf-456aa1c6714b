import Round1 from '../../../layouts/RoundBase/Round1';
import React from 'react';
import Host from '../../../layouts/Host/Host';
import QuestionBoxRound1 from '../../../layouts/RoundBase/Round1';
import HostQuestionBoxRoundTurn from '../../../layouts/RoundBase/Host/HostQuestionBoxRoundTurn';

const HostRoundTurn: React.FC = () => {
    return (
        <Host
            QuestionComponent={<HostQuestionBoxRoundTurn isHost={true}/>}
        />
    )
};

export default HostRoundTurn;