{"ast": null, "code": "// Game Redux slice\nimport { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\n// Initial state\nconst initialState = {\n  // Current game status\n  currentRound: 1,\n  isActive: false,\n  isHost: false,\n  // Questions and answers\n  currentQuestion: null,\n  questions: [],\n  currentCorrectAnswer: null,\n  // Players and scoring\n  players: [],\n  scores: [],\n  scoreRules: null,\n  // Round-specific data\n  round2Grid: null,\n  round4Grid: null,\n  // Game settings\n  mode: 'manual',\n  timeLimit: 30,\n  // UI state\n  showRules: false,\n  currentTurn: 1,\n  questionNumber: 1,\n  // Loading states\n  loading: {\n    isLoading: false,\n    error: null\n  }\n};\n\n// Async thunks\nexport const fetchQuestions = createAsyncThunk('game/fetchQuestions', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch(`/api/game/questions?testName=${params.testName}&round=${params.round}&difficulty=${params.difficulty || ''}`);\n    if (!response.ok) {\n      throw new Error('Failed to fetch questions');\n    }\n    const data = await response.json();\n    return data.questions;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const submitAnswer = createAsyncThunk('game/submitAnswer', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/answer', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to submit answer');\n    }\n    const data = await response.json();\n    return data;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\nexport const updateScores = createAsyncThunk('game/updateScores', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // TODO: Replace with actual API call\n    const response = await fetch('/api/game/scoring', {\n      method: 'POST',\n      headers: {\n        'Content-Type': 'application/json'\n      },\n      body: JSON.stringify(params)\n    });\n    if (!response.ok) {\n      throw new Error('Failed to update scores');\n    }\n    const data = await response.json();\n    return data.scores;\n  } catch (error) {\n    return rejectWithValue(error.message);\n  }\n});\n\n// Game slice\nconst gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Game state management\n    setCurrentRound: (state, action) => {\n      state.currentRound = action.payload;\n      state.questionNumber = 1; // Reset question number when round changes\n    },\n    setIsActive: (state, action) => {\n      state.isActive = action.payload;\n    },\n    setIsHost: (state, action) => {\n      state.isHost = action.payload;\n    },\n    // Question management\n    setCurrentQuestion: (state, action) => {\n      state.currentQuestion = action.payload;\n    },\n    setQuestions: (state, action) => {\n      state.questions = action.payload;\n    },\n    setCurrentCorrectAnswer: (state, action) => {\n      state.currentCorrectAnswer = action.payload;\n    },\n    nextQuestion: state => {\n      state.questionNumber += 1;\n    },\n    setQuestionNumber: (state, action) => {\n      state.questionNumber = action.payload;\n    },\n    // Player management\n    setPlayers: (state, action) => {\n      state.players = action.payload;\n    },\n    updatePlayer: (state, action) => {\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (playerIndex !== -1) {\n        state.players[playerIndex] = {\n          ...state.players[playerIndex],\n          ...action.payload.updates\n        };\n      }\n    },\n    addPlayer: (state, action) => {\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (existingIndex === -1) {\n        state.players.push(action.payload);\n      }\n    },\n    removePlayer: (state, action) => {\n      state.players = state.players.filter(p => p.uid !== action.payload);\n    },\n    // Scoring\n    setScores: (state, action) => {\n      state.scores = action.payload;\n    },\n    setScoreRules: (state, action) => {\n      state.scoreRules = action.payload;\n    },\n    // Round-specific data\n    setRound2Grid: (state, action) => {\n      state.round2Grid = action.payload;\n    },\n    setRound4Grid: (state, action) => {\n      state.round4Grid = action.payload;\n    },\n    // Game settings\n    setMode: (state, action) => {\n      state.mode = action.payload;\n    },\n    setTimeLimit: (state, action) => {\n      state.timeLimit = action.payload;\n    },\n    // UI state\n    setShowRules: (state, action) => {\n      state.showRules = action.payload;\n    },\n    setCurrentTurn: (state, action) => {\n      state.currentTurn = action.payload;\n    },\n    // Reset game state\n    resetGame: state => {\n      return {\n        ...initialState,\n        isHost: state.isHost\n      };\n    },\n    // Error handling\n    clearError: state => {\n      state.loading.error = null;\n    }\n  },\n  extraReducers: builder => {\n    // Fetch questions\n    builder.addCase(fetchQuestions.pending, state => {\n      state.loading.isLoading = true;\n      state.loading.error = null;\n    }).addCase(fetchQuestions.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      state.questions = action.payload;\n    }).addCase(fetchQuestions.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Submit answer\n    builder.addCase(submitAnswer.pending, state => {\n      state.loading.isLoading = true;\n    }).addCase(submitAnswer.fulfilled, (state, action) => {\n      state.loading.isLoading = false;\n      // Handle answer submission result\n    }).addCase(submitAnswer.rejected, (state, action) => {\n      state.loading.isLoading = false;\n      state.loading.error = action.payload;\n    });\n\n    // Update scores\n    builder.addCase(updateScores.fulfilled, (state, action) => {\n      state.scores = action.payload;\n    }).addCase(updateScores.rejected, (state, action) => {\n      state.loading.error = action.payload;\n    });\n  }\n});\nexport const {\n  setCurrentRound,\n  setIsActive,\n  setIsHost,\n  setCurrentQuestion,\n  setQuestions,\n  setCurrentCorrectAnswer,\n  nextQuestion,\n  setQuestionNumber,\n  setPlayers,\n  updatePlayer,\n  addPlayer,\n  removePlayer,\n  setScores,\n  setScoreRules,\n  setRound2Grid,\n  setRound4Grid,\n  setMode,\n  setTimeLimit,\n  setShowRules,\n  setCurrentTurn,\n  resetGame,\n  clearError\n} = gameSlice.actions;\nexport default gameSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "initialState", "currentRound", "isActive", "isHost", "currentQuestion", "questions", "currentCorrectAnswer", "players", "scores", "scoreRules", "round2Grid", "round4Grid", "mode", "timeLimit", "showRules", "currentTurn", "questionNumber", "loading", "isLoading", "error", "fetchQuestions", "params", "rejectWithValue", "response", "fetch", "testName", "round", "difficulty", "ok", "Error", "data", "json", "message", "submitAnswer", "method", "headers", "body", "JSON", "stringify", "updateScores", "gameSlice", "name", "reducers", "setCurrentRound", "state", "action", "payload", "setIsActive", "setIsHost", "setCurrentQuestion", "setQuestions", "setCurrentCorrectAnswer", "nextQuestion", "setQuestionNumber", "setPlayers", "updatePlayer", "playerIndex", "findIndex", "p", "uid", "updates", "addPlayer", "existingIndex", "push", "removePlayer", "filter", "setScores", "setScoreRules", "setRound2Grid", "setRound4Grid", "setMode", "setTimeLimit", "setShowRules", "setCurrentTurn", "resetGame", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/gameSlice.ts"], "sourcesContent": ["// Game Redux slice\nimport { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\nimport { GameState, Question, Score, PlayerData, Round2Grid, Round4Grid, ScoreRule } from '../../../shared/types';\n\n// Initial state\nconst initialState: GameState = {\n  // Current game status\n  currentRound: 1,\n  isActive: false,\n  isHost: false,\n  \n  // Questions and answers\n  currentQuestion: null,\n  questions: [],\n  currentCorrectAnswer: null,\n  \n  // Players and scoring\n  players: [],\n  scores: [],\n  scoreRules: null,\n  \n  // Round-specific data\n  round2Grid: null,\n  round4Grid: null,\n  \n  // Game settings\n  mode: 'manual',\n  timeLimit: 30,\n  \n  // UI state\n  showRules: false,\n  currentTurn: 1,\n  questionNumber: 1,\n  \n  // Loading states\n  loading: {\n    isLoading: false,\n    error: null,\n  },\n};\n\n// Async thunks\nexport const fetchQuestions = createAsyncThunk(\n  'game/fetchQuestions',\n  async (params: { testName: string; round: number; difficulty?: string }, { rejectWithValue }) => {\n    try {\n      // TODO: Replace with actual API call\n      const response = await fetch(`/api/game/questions?testName=${params.testName}&round=${params.round}&difficulty=${params.difficulty || ''}`);\n      \n      if (!response.ok) {\n        throw new Error('Failed to fetch questions');\n      }\n      \n      const data = await response.json();\n      return data.questions;\n    } catch (error: any) {\n      return rejectWithValue(error.message);\n    }\n  }\n);\n\nexport const submitAnswer = createAsyncThunk(\n  'game/submitAnswer',\n  async (params: { roomId: string; uid: string; answer: string; time: number }, { rejectWithValue }) => {\n    try {\n      // TODO: Replace with actual API call\n      const response = await fetch('/api/game/answer', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(params),\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to submit answer');\n      }\n      \n      const data = await response.json();\n      return data;\n    } catch (error: any) {\n      return rejectWithValue(error.message);\n    }\n  }\n);\n\nexport const updateScores = createAsyncThunk(\n  'game/updateScores',\n  async (params: { roomId: string; mode: string; scores?: Score[]; round: string }, { rejectWithValue }) => {\n    try {\n      // TODO: Replace with actual API call\n      const response = await fetch('/api/game/scoring', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(params),\n      });\n      \n      if (!response.ok) {\n        throw new Error('Failed to update scores');\n      }\n      \n      const data = await response.json();\n      return data.scores;\n    } catch (error: any) {\n      return rejectWithValue(error.message);\n    }\n  }\n);\n\n// Game slice\nconst gameSlice = createSlice({\n  name: 'game',\n  initialState,\n  reducers: {\n    // Game state management\n    setCurrentRound: (state, action: PayloadAction<number>) => {\n      state.currentRound = action.payload;\n      state.questionNumber = 1; // Reset question number when round changes\n    },\n    \n    setIsActive: (state, action: PayloadAction<boolean>) => {\n      state.isActive = action.payload;\n    },\n    \n    setIsHost: (state, action: PayloadAction<boolean>) => {\n      state.isHost = action.payload;\n    },\n    \n    // Question management\n    setCurrentQuestion: (state, action: PayloadAction<Question | null>) => {\n      state.currentQuestion = action.payload;\n    },\n    \n    setQuestions: (state, action: PayloadAction<Question[]>) => {\n      state.questions = action.payload;\n    },\n    \n    setCurrentCorrectAnswer: (state, action: PayloadAction<string[] | null>) => {\n      state.currentCorrectAnswer = action.payload;\n    },\n    \n    nextQuestion: (state) => {\n      state.questionNumber += 1;\n    },\n    \n    setQuestionNumber: (state, action: PayloadAction<number>) => {\n      state.questionNumber = action.payload;\n    },\n    \n    // Player management\n    setPlayers: (state, action: PayloadAction<PlayerData[]>) => {\n      state.players = action.payload;\n    },\n    \n    updatePlayer: (state, action: PayloadAction<{ uid: string; updates: Partial<PlayerData> }>) => {\n      const playerIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (playerIndex !== -1) {\n        state.players[playerIndex] = { ...state.players[playerIndex], ...action.payload.updates };\n      }\n    },\n    \n    addPlayer: (state, action: PayloadAction<PlayerData>) => {\n      const existingIndex = state.players.findIndex(p => p.uid === action.payload.uid);\n      if (existingIndex === -1) {\n        state.players.push(action.payload);\n      }\n    },\n    \n    removePlayer: (state, action: PayloadAction<string>) => {\n      state.players = state.players.filter(p => p.uid !== action.payload);\n    },\n    \n    // Scoring\n    setScores: (state, action: PayloadAction<Score[]>) => {\n      state.scores = action.payload;\n    },\n    \n    setScoreRules: (state, action: PayloadAction<ScoreRule>) => {\n      state.scoreRules = action.payload;\n    },\n    \n    // Round-specific data\n    setRound2Grid: (state, action: PayloadAction<Round2Grid | null>) => {\n      state.round2Grid = action.payload;\n    },\n    \n    setRound4Grid: (state, action: PayloadAction<Round4Grid | null>) => {\n      state.round4Grid = action.payload;\n    },\n    \n    // Game settings\n    setMode: (state, action: PayloadAction<'manual' | 'auto' | 'adaptive'>) => {\n      state.mode = action.payload;\n    },\n    \n    setTimeLimit: (state, action: PayloadAction<number>) => {\n      state.timeLimit = action.payload;\n    },\n    \n    // UI state\n    setShowRules: (state, action: PayloadAction<boolean>) => {\n      state.showRules = action.payload;\n    },\n    \n    setCurrentTurn: (state, action: PayloadAction<number>) => {\n      state.currentTurn = action.payload;\n    },\n    \n    // Reset game state\n    resetGame: (state) => {\n      return { ...initialState, isHost: state.isHost };\n    },\n    \n    // Error handling\n    clearError: (state) => {\n      state.loading.error = null;\n    },\n  },\n  \n  extraReducers: (builder) => {\n    // Fetch questions\n    builder\n      .addCase(fetchQuestions.pending, (state) => {\n        state.loading.isLoading = true;\n        state.loading.error = null;\n      })\n      .addCase(fetchQuestions.fulfilled, (state, action) => {\n        state.loading.isLoading = false;\n        state.questions = action.payload;\n      })\n      .addCase(fetchQuestions.rejected, (state, action) => {\n        state.loading.isLoading = false;\n        state.loading.error = action.payload as string;\n      });\n    \n    // Submit answer\n    builder\n      .addCase(submitAnswer.pending, (state) => {\n        state.loading.isLoading = true;\n      })\n      .addCase(submitAnswer.fulfilled, (state, action) => {\n        state.loading.isLoading = false;\n        // Handle answer submission result\n      })\n      .addCase(submitAnswer.rejected, (state, action) => {\n        state.loading.isLoading = false;\n        state.loading.error = action.payload as string;\n      });\n    \n    // Update scores\n    builder\n      .addCase(updateScores.fulfilled, (state, action) => {\n        state.scores = action.payload;\n      })\n      .addCase(updateScores.rejected, (state, action) => {\n        state.loading.error = action.payload as string;\n      });\n  },\n});\n\nexport const {\n  setCurrentRound,\n  setIsActive,\n  setIsHost,\n  setCurrentQuestion,\n  setQuestions,\n  setCurrentCorrectAnswer,\n  nextQuestion,\n  setQuestionNumber,\n  setPlayers,\n  updatePlayer,\n  addPlayer,\n  removePlayer,\n  setScores,\n  setScoreRules,\n  setRound2Grid,\n  setRound4Grid,\n  setMode,\n  setTimeLimit,\n  setShowRules,\n  setCurrentTurn,\n  resetGame,\n  clearError,\n} = gameSlice.actions;\n\nexport default gameSlice.reducer;\n"], "mappings": "AAAA;AACA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAG/E;AACA,MAAMC,YAAuB,GAAG;EAC9B;EACAC,YAAY,EAAE,CAAC;EACfC,QAAQ,EAAE,KAAK;EACfC,MAAM,EAAE,KAAK;EAEb;EACAC,eAAe,EAAE,IAAI;EACrBC,SAAS,EAAE,EAAE;EACbC,oBAAoB,EAAE,IAAI;EAE1B;EACAC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,EAAE;EACVC,UAAU,EAAE,IAAI;EAEhB;EACAC,UAAU,EAAE,IAAI;EAChBC,UAAU,EAAE,IAAI;EAEhB;EACAC,IAAI,EAAE,QAAQ;EACdC,SAAS,EAAE,EAAE;EAEb;EACAC,SAAS,EAAE,KAAK;EAChBC,WAAW,EAAE,CAAC;EACdC,cAAc,EAAE,CAAC;EAEjB;EACAC,OAAO,EAAE;IACPC,SAAS,EAAE,KAAK;IAChBC,KAAK,EAAE;EACT;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAGrB,gBAAgB,CAC5C,qBAAqB,EACrB,OAAOsB,MAAgE,EAAE;EAAEC;AAAgB,CAAC,KAAK;EAC/F,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,gCAAgCH,MAAM,CAACI,QAAQ,UAAUJ,MAAM,CAACK,KAAK,eAAeL,MAAM,CAACM,UAAU,IAAI,EAAE,EAAE,CAAC;IAE3I,IAAI,CAACJ,QAAQ,CAACK,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,2BAA2B,CAAC;IAC9C;IAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI,CAACzB,SAAS;EACvB,CAAC,CAAC,OAAOc,KAAU,EAAE;IACnB,OAAOG,eAAe,CAACH,KAAK,CAACa,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMC,YAAY,GAAGlC,gBAAgB,CAC1C,mBAAmB,EACnB,OAAOsB,MAAqE,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACpG,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,kBAAkB,EAAE;MAC/CU,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjB,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACE,QAAQ,CAACK,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOX,KAAU,EAAE;IACnB,OAAOG,eAAe,CAACH,KAAK,CAACa,OAAO,CAAC;EACvC;AACF,CACF,CAAC;AAED,OAAO,MAAMO,YAAY,GAAGxC,gBAAgB,CAC1C,mBAAmB,EACnB,OAAOsB,MAAyE,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACxG,IAAI;IACF;IACA,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,mBAAmB,EAAE;MAChDU,MAAM,EAAE,MAAM;MACdC,OAAO,EAAE;QAAE,cAAc,EAAE;MAAmB,CAAC;MAC/CC,IAAI,EAAEC,IAAI,CAACC,SAAS,CAACjB,MAAM;IAC7B,CAAC,CAAC;IAEF,IAAI,CAACE,QAAQ,CAACK,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;IAC5C;IAEA,MAAMC,IAAI,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;IAClC,OAAOD,IAAI,CAACtB,MAAM;EACpB,CAAC,CAAC,OAAOW,KAAU,EAAE;IACnB,OAAOG,eAAe,CAACH,KAAK,CAACa,OAAO,CAAC;EACvC;AACF,CACF,CAAC;;AAED;AACA,MAAMQ,SAAS,GAAG1C,WAAW,CAAC;EAC5B2C,IAAI,EAAE,MAAM;EACZzC,YAAY;EACZ0C,QAAQ,EAAE;IACR;IACAC,eAAe,EAAEA,CAACC,KAAK,EAAEC,MAA6B,KAAK;MACzDD,KAAK,CAAC3C,YAAY,GAAG4C,MAAM,CAACC,OAAO;MACnCF,KAAK,CAAC5B,cAAc,GAAG,CAAC,CAAC,CAAC;IAC5B,CAAC;IAED+B,WAAW,EAAEA,CAACH,KAAK,EAAEC,MAA8B,KAAK;MACtDD,KAAK,CAAC1C,QAAQ,GAAG2C,MAAM,CAACC,OAAO;IACjC,CAAC;IAEDE,SAAS,EAAEA,CAACJ,KAAK,EAAEC,MAA8B,KAAK;MACpDD,KAAK,CAACzC,MAAM,GAAG0C,MAAM,CAACC,OAAO;IAC/B,CAAC;IAED;IACAG,kBAAkB,EAAEA,CAACL,KAAK,EAAEC,MAAsC,KAAK;MACrED,KAAK,CAACxC,eAAe,GAAGyC,MAAM,CAACC,OAAO;IACxC,CAAC;IAEDI,YAAY,EAAEA,CAACN,KAAK,EAAEC,MAAiC,KAAK;MAC1DD,KAAK,CAACvC,SAAS,GAAGwC,MAAM,CAACC,OAAO;IAClC,CAAC;IAEDK,uBAAuB,EAAEA,CAACP,KAAK,EAAEC,MAAsC,KAAK;MAC1ED,KAAK,CAACtC,oBAAoB,GAAGuC,MAAM,CAACC,OAAO;IAC7C,CAAC;IAEDM,YAAY,EAAGR,KAAK,IAAK;MACvBA,KAAK,CAAC5B,cAAc,IAAI,CAAC;IAC3B,CAAC;IAEDqC,iBAAiB,EAAEA,CAACT,KAAK,EAAEC,MAA6B,KAAK;MAC3DD,KAAK,CAAC5B,cAAc,GAAG6B,MAAM,CAACC,OAAO;IACvC,CAAC;IAED;IACAQ,UAAU,EAAEA,CAACV,KAAK,EAAEC,MAAmC,KAAK;MAC1DD,KAAK,CAACrC,OAAO,GAAGsC,MAAM,CAACC,OAAO;IAChC,CAAC;IAEDS,YAAY,EAAEA,CAACX,KAAK,EAAEC,MAAoE,KAAK;MAC7F,MAAMW,WAAW,GAAGZ,KAAK,CAACrC,OAAO,CAACkD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKd,MAAM,CAACC,OAAO,CAACa,GAAG,CAAC;MAC9E,IAAIH,WAAW,KAAK,CAAC,CAAC,EAAE;QACtBZ,KAAK,CAACrC,OAAO,CAACiD,WAAW,CAAC,GAAG;UAAE,GAAGZ,KAAK,CAACrC,OAAO,CAACiD,WAAW,CAAC;UAAE,GAAGX,MAAM,CAACC,OAAO,CAACc;QAAQ,CAAC;MAC3F;IACF,CAAC;IAEDC,SAAS,EAAEA,CAACjB,KAAK,EAAEC,MAAiC,KAAK;MACvD,MAAMiB,aAAa,GAAGlB,KAAK,CAACrC,OAAO,CAACkD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKd,MAAM,CAACC,OAAO,CAACa,GAAG,CAAC;MAChF,IAAIG,aAAa,KAAK,CAAC,CAAC,EAAE;QACxBlB,KAAK,CAACrC,OAAO,CAACwD,IAAI,CAAClB,MAAM,CAACC,OAAO,CAAC;MACpC;IACF,CAAC;IAEDkB,YAAY,EAAEA,CAACpB,KAAK,EAAEC,MAA6B,KAAK;MACtDD,KAAK,CAACrC,OAAO,GAAGqC,KAAK,CAACrC,OAAO,CAAC0D,MAAM,CAACP,CAAC,IAAIA,CAAC,CAACC,GAAG,KAAKd,MAAM,CAACC,OAAO,CAAC;IACrE,CAAC;IAED;IACAoB,SAAS,EAAEA,CAACtB,KAAK,EAAEC,MAA8B,KAAK;MACpDD,KAAK,CAACpC,MAAM,GAAGqC,MAAM,CAACC,OAAO;IAC/B,CAAC;IAEDqB,aAAa,EAAEA,CAACvB,KAAK,EAAEC,MAAgC,KAAK;MAC1DD,KAAK,CAACnC,UAAU,GAAGoC,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACAsB,aAAa,EAAEA,CAACxB,KAAK,EAAEC,MAAwC,KAAK;MAClED,KAAK,CAAClC,UAAU,GAAGmC,MAAM,CAACC,OAAO;IACnC,CAAC;IAEDuB,aAAa,EAAEA,CAACzB,KAAK,EAAEC,MAAwC,KAAK;MAClED,KAAK,CAACjC,UAAU,GAAGkC,MAAM,CAACC,OAAO;IACnC,CAAC;IAED;IACAwB,OAAO,EAAEA,CAAC1B,KAAK,EAAEC,MAAqD,KAAK;MACzED,KAAK,CAAChC,IAAI,GAAGiC,MAAM,CAACC,OAAO;IAC7B,CAAC;IAEDyB,YAAY,EAAEA,CAAC3B,KAAK,EAAEC,MAA6B,KAAK;MACtDD,KAAK,CAAC/B,SAAS,GAAGgC,MAAM,CAACC,OAAO;IAClC,CAAC;IAED;IACA0B,YAAY,EAAEA,CAAC5B,KAAK,EAAEC,MAA8B,KAAK;MACvDD,KAAK,CAAC9B,SAAS,GAAG+B,MAAM,CAACC,OAAO;IAClC,CAAC;IAED2B,cAAc,EAAEA,CAAC7B,KAAK,EAAEC,MAA6B,KAAK;MACxDD,KAAK,CAAC7B,WAAW,GAAG8B,MAAM,CAACC,OAAO;IACpC,CAAC;IAED;IACA4B,SAAS,EAAG9B,KAAK,IAAK;MACpB,OAAO;QAAE,GAAG5C,YAAY;QAAEG,MAAM,EAAEyC,KAAK,CAACzC;MAAO,CAAC;IAClD,CAAC;IAED;IACAwE,UAAU,EAAG/B,KAAK,IAAK;MACrBA,KAAK,CAAC3B,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B;EACF,CAAC;EAEDyD,aAAa,EAAGC,OAAO,IAAK;IAC1B;IACAA,OAAO,CACJC,OAAO,CAAC1D,cAAc,CAAC2D,OAAO,EAAGnC,KAAK,IAAK;MAC1CA,KAAK,CAAC3B,OAAO,CAACC,SAAS,GAAG,IAAI;MAC9B0B,KAAK,CAAC3B,OAAO,CAACE,KAAK,GAAG,IAAI;IAC5B,CAAC,CAAC,CACD2D,OAAO,CAAC1D,cAAc,CAAC4D,SAAS,EAAE,CAACpC,KAAK,EAAEC,MAAM,KAAK;MACpDD,KAAK,CAAC3B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B0B,KAAK,CAACvC,SAAS,GAAGwC,MAAM,CAACC,OAAO;IAClC,CAAC,CAAC,CACDgC,OAAO,CAAC1D,cAAc,CAAC6D,QAAQ,EAAE,CAACrC,KAAK,EAAEC,MAAM,KAAK;MACnDD,KAAK,CAAC3B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B0B,KAAK,CAAC3B,OAAO,CAACE,KAAK,GAAG0B,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA+B,OAAO,CACJC,OAAO,CAAC7C,YAAY,CAAC8C,OAAO,EAAGnC,KAAK,IAAK;MACxCA,KAAK,CAAC3B,OAAO,CAACC,SAAS,GAAG,IAAI;IAChC,CAAC,CAAC,CACD4D,OAAO,CAAC7C,YAAY,CAAC+C,SAAS,EAAE,CAACpC,KAAK,EAAEC,MAAM,KAAK;MAClDD,KAAK,CAAC3B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B;IACF,CAAC,CAAC,CACD4D,OAAO,CAAC7C,YAAY,CAACgD,QAAQ,EAAE,CAACrC,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAAC3B,OAAO,CAACC,SAAS,GAAG,KAAK;MAC/B0B,KAAK,CAAC3B,OAAO,CAACE,KAAK,GAAG0B,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;;IAEJ;IACA+B,OAAO,CACJC,OAAO,CAACvC,YAAY,CAACyC,SAAS,EAAE,CAACpC,KAAK,EAAEC,MAAM,KAAK;MAClDD,KAAK,CAACpC,MAAM,GAAGqC,MAAM,CAACC,OAAO;IAC/B,CAAC,CAAC,CACDgC,OAAO,CAACvC,YAAY,CAAC0C,QAAQ,EAAE,CAACrC,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAAC3B,OAAO,CAACE,KAAK,GAAG0B,MAAM,CAACC,OAAiB;IAChD,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXH,eAAe;EACfI,WAAW;EACXC,SAAS;EACTC,kBAAkB;EAClBC,YAAY;EACZC,uBAAuB;EACvBC,YAAY;EACZC,iBAAiB;EACjBC,UAAU;EACVC,YAAY;EACZM,SAAS;EACTG,YAAY;EACZE,SAAS;EACTC,aAAa;EACbC,aAAa;EACbC,aAAa;EACbC,OAAO;EACPC,YAAY;EACZC,YAAY;EACZC,cAAc;EACdC,SAAS;EACTC;AACF,CAAC,GAAGnC,SAAS,CAAC0C,OAAO;AAErB,eAAe1C,SAAS,CAAC2C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}