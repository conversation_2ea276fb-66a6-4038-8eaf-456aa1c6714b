{"ast": null, "code": "// UI Redux slice for managing global UI state\nimport { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  modals: {},\n  toasts: [],\n  globalLoading: false,\n  theme: 'auto',\n  soundEnabled: true,\n  currentPage: '',\n  previousPage: '',\n  showPlayerList: true,\n  showScoreboard: true,\n  showGameControls: true,\n  selectedTopic: '',\n  animationKey: 0,\n  forms: {},\n  sidebarOpen: false,\n  mobileMenuOpen: false\n};\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    // Modal management\n    openModal: (state, action) => {\n      state.modals[action.payload.id] = {\n        isOpen: true,\n        type: action.payload.type,\n        data: action.payload.data || null\n      };\n    },\n    closeModal: (state, action) => {\n      if (state.modals[action.payload]) {\n        state.modals[action.payload].isOpen = false;\n      }\n    },\n    updateModalData: (state, action) => {\n      if (state.modals[action.payload.id]) {\n        state.modals[action.payload.id].data = action.payload.data;\n      }\n    },\n    clearModal: (state, action) => {\n      delete state.modals[action.payload];\n    },\n    // Toast notifications\n    addToast: (state, action) => {\n      var _action$payload$autoC, _action$payload$durat;\n      const toast = {\n        ...action.payload,\n        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n        autoClose: (_action$payload$autoC = action.payload.autoClose) !== null && _action$payload$autoC !== void 0 ? _action$payload$autoC : true,\n        duration: (_action$payload$durat = action.payload.duration) !== null && _action$payload$durat !== void 0 ? _action$payload$durat : 5000\n      };\n      state.toasts.push(toast);\n    },\n    removeToast: (state, action) => {\n      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);\n    },\n    clearAllToasts: state => {\n      state.toasts = [];\n    },\n    // Global loading\n    setGlobalLoading: (state, action) => {\n      state.globalLoading = action.payload;\n    },\n    // Theme and preferences\n    setTheme: (state, action) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    setSoundEnabled: (state, action) => {\n      state.soundEnabled = action.payload;\n      localStorage.setItem('soundEnabled', action.payload.toString());\n    },\n    // Navigation\n    setCurrentPage: (state, action) => {\n      state.previousPage = state.currentPage;\n      state.currentPage = action.payload;\n    },\n    // Game UI\n    setShowPlayerList: (state, action) => {\n      state.showPlayerList = action.payload;\n    },\n    setShowScoreboard: (state, action) => {\n      state.showScoreboard = action.payload;\n    },\n    setShowGameControls: (state, action) => {\n      state.showGameControls = action.payload;\n    },\n    // Round-specific UI\n    setSelectedTopic: (state, action) => {\n      state.selectedTopic = action.payload;\n    },\n    incrementAnimationKey: state => {\n      state.animationKey += 1;\n    },\n    setAnimationKey: (state, action) => {\n      state.animationKey = action.payload;\n    },\n    // Form management\n    setFormSubmitting: (state, action) => {\n      if (!state.forms[action.payload.formId]) {\n        state.forms[action.payload.formId] = {\n          isSubmitting: false,\n          errors: {}\n        };\n      }\n      state.forms[action.payload.formId].isSubmitting = action.payload.isSubmitting;\n    },\n    setFormErrors: (state, action) => {\n      if (!state.forms[action.payload.formId]) {\n        state.forms[action.payload.formId] = {\n          isSubmitting: false,\n          errors: {}\n        };\n      }\n      state.forms[action.payload.formId].errors = action.payload.errors;\n    },\n    clearFormErrors: (state, action) => {\n      if (state.forms[action.payload]) {\n        state.forms[action.payload].errors = {};\n      }\n    },\n    clearForm: (state, action) => {\n      delete state.forms[action.payload];\n    },\n    // Sidebar and navigation\n    setSidebarOpen: (state, action) => {\n      state.sidebarOpen = action.payload;\n    },\n    setMobileMenuOpen: (state, action) => {\n      state.mobileMenuOpen = action.payload;\n    },\n    toggleSidebar: state => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    toggleMobileMenu: state => {\n      state.mobileMenuOpen = !state.mobileMenuOpen;\n    },\n    // Reset UI state\n    resetUI: state => {\n      return {\n        ...initialState,\n        theme: state.theme,\n        soundEnabled: state.soundEnabled\n      };\n    }\n  }\n});\nexport const {\n  // Modal actions\n  openModal,\n  closeModal,\n  updateModalData,\n  clearModal,\n  // Toast actions\n  addToast,\n  removeToast,\n  clearAllToasts,\n  // Global loading\n  setGlobalLoading,\n  // Theme and preferences\n  setTheme,\n  setSoundEnabled,\n  // Navigation\n  setCurrentPage,\n  // Game UI\n  setShowPlayerList,\n  setShowScoreboard,\n  setShowGameControls,\n  // Round-specific UI\n  setSelectedTopic,\n  incrementAnimationKey,\n  setAnimationKey,\n  // Form management\n  setFormSubmitting,\n  setFormErrors,\n  clearFormErrors,\n  clearForm,\n  // Sidebar and navigation\n  setSidebarOpen,\n  setMobileMenuOpen,\n  toggleSidebar,\n  toggleMobileMenu,\n  // Reset\n  resetUI\n} = uiSlice.actions;\nexport default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "modals", "toasts", "globalLoading", "theme", "soundEnabled", "currentPage", "previousPage", "showPlayerList", "showScoreboard", "showGameControls", "selectedTopic", "animationKey", "forms", "sidebarOpen", "mobileMenuOpen", "uiSlice", "name", "reducers", "openModal", "state", "action", "payload", "id", "isOpen", "type", "data", "closeModal", "updateModalData", "clearModal", "addToast", "_action$payload$autoC", "_action$payload$durat", "toast", "Date", "now", "toString", "Math", "random", "substr", "autoClose", "duration", "push", "removeToast", "filter", "clearAllToasts", "setGlobalLoading", "setTheme", "localStorage", "setItem", "setSoundEnabled", "setCurrentPage", "setShowPlayerList", "setShowScoreboard", "setShowGameControls", "setSelectedTopic", "incrementAnimationKey", "setAnimationKey", "setFormSubmitting", "formId", "isSubmitting", "errors", "setFormErrors", "clearFormErrors", "clearForm", "setSidebarOpen", "setMobileMenuOpen", "toggleSidebar", "toggleMobileMenu", "resetUI", "actions", "reducer"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/slices/uiSlice.ts"], "sourcesContent": ["// UI Redux slice for managing global UI state\nimport { createSlice, PayloadAction } from '@reduxjs/toolkit';\nimport { ModalState, ToastNotification } from '../../../shared/types';\n\ninterface UIState {\n  // Modal management\n  modals: {\n    [key: string]: ModalState;\n  };\n  \n  // Toast notifications\n  toasts: ToastNotification[];\n  \n  // Loading states for global operations\n  globalLoading: boolean;\n  \n  // Theme and preferences\n  theme: 'light' | 'dark' | 'auto';\n  soundEnabled: boolean;\n  \n  // Navigation and routing\n  currentPage: string;\n  previousPage: string;\n  \n  // Game UI specific\n  showPlayerList: boolean;\n  showScoreboard: boolean;\n  showGameControls: boolean;\n  \n  // Round-specific UI\n  selectedTopic: string;\n  animationKey: number;\n  \n  // Form states\n  forms: {\n    [formId: string]: {\n      isSubmitting: boolean;\n      errors: { [field: string]: string };\n    };\n  };\n  \n  // Sidebar and navigation\n  sidebarOpen: boolean;\n  mobileMenuOpen: boolean;\n}\n\nconst initialState: UIState = {\n  modals: {},\n  toasts: [],\n  globalLoading: false,\n  theme: 'auto',\n  soundEnabled: true,\n  currentPage: '',\n  previousPage: '',\n  showPlayerList: true,\n  showScoreboard: true,\n  showGameControls: true,\n  selectedTopic: '',\n  animationKey: 0,\n  forms: {},\n  sidebarOpen: false,\n  mobileMenuOpen: false,\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    // Modal management\n    openModal: (state, action: PayloadAction<{ id: string; type: string; data?: any }>) => {\n      state.modals[action.payload.id] = {\n        isOpen: true,\n        type: action.payload.type,\n        data: action.payload.data || null,\n      };\n    },\n    \n    closeModal: (state, action: PayloadAction<string>) => {\n      if (state.modals[action.payload]) {\n        state.modals[action.payload].isOpen = false;\n      }\n    },\n    \n    updateModalData: (state, action: PayloadAction<{ id: string; data: any }>) => {\n      if (state.modals[action.payload.id]) {\n        state.modals[action.payload.id].data = action.payload.data;\n      }\n    },\n    \n    clearModal: (state, action: PayloadAction<string>) => {\n      delete state.modals[action.payload];\n    },\n    \n    // Toast notifications\n    addToast: (state, action: PayloadAction<Omit<ToastNotification, 'id'>>) => {\n      const toast: ToastNotification = {\n        ...action.payload,\n        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),\n        autoClose: action.payload.autoClose ?? true,\n        duration: action.payload.duration ?? 5000,\n      };\n      state.toasts.push(toast);\n    },\n    \n    removeToast: (state, action: PayloadAction<string>) => {\n      state.toasts = state.toasts.filter(toast => toast.id !== action.payload);\n    },\n    \n    clearAllToasts: (state) => {\n      state.toasts = [];\n    },\n    \n    // Global loading\n    setGlobalLoading: (state, action: PayloadAction<boolean>) => {\n      state.globalLoading = action.payload;\n    },\n    \n    // Theme and preferences\n    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'auto'>) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    \n    setSoundEnabled: (state, action: PayloadAction<boolean>) => {\n      state.soundEnabled = action.payload;\n      localStorage.setItem('soundEnabled', action.payload.toString());\n    },\n    \n    // Navigation\n    setCurrentPage: (state, action: PayloadAction<string>) => {\n      state.previousPage = state.currentPage;\n      state.currentPage = action.payload;\n    },\n    \n    // Game UI\n    setShowPlayerList: (state, action: PayloadAction<boolean>) => {\n      state.showPlayerList = action.payload;\n    },\n    \n    setShowScoreboard: (state, action: PayloadAction<boolean>) => {\n      state.showScoreboard = action.payload;\n    },\n    \n    setShowGameControls: (state, action: PayloadAction<boolean>) => {\n      state.showGameControls = action.payload;\n    },\n    \n    // Round-specific UI\n    setSelectedTopic: (state, action: PayloadAction<string>) => {\n      state.selectedTopic = action.payload;\n    },\n    \n    incrementAnimationKey: (state) => {\n      state.animationKey += 1;\n    },\n    \n    setAnimationKey: (state, action: PayloadAction<number>) => {\n      state.animationKey = action.payload;\n    },\n    \n    // Form management\n    setFormSubmitting: (state, action: PayloadAction<{ formId: string; isSubmitting: boolean }>) => {\n      if (!state.forms[action.payload.formId]) {\n        state.forms[action.payload.formId] = { isSubmitting: false, errors: {} };\n      }\n      state.forms[action.payload.formId].isSubmitting = action.payload.isSubmitting;\n    },\n    \n    setFormErrors: (state, action: PayloadAction<{ formId: string; errors: { [field: string]: string } }>) => {\n      if (!state.forms[action.payload.formId]) {\n        state.forms[action.payload.formId] = { isSubmitting: false, errors: {} };\n      }\n      state.forms[action.payload.formId].errors = action.payload.errors;\n    },\n    \n    clearFormErrors: (state, action: PayloadAction<string>) => {\n      if (state.forms[action.payload]) {\n        state.forms[action.payload].errors = {};\n      }\n    },\n    \n    clearForm: (state, action: PayloadAction<string>) => {\n      delete state.forms[action.payload];\n    },\n    \n    // Sidebar and navigation\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    \n    setMobileMenuOpen: (state, action: PayloadAction<boolean>) => {\n      state.mobileMenuOpen = action.payload;\n    },\n    \n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    \n    toggleMobileMenu: (state) => {\n      state.mobileMenuOpen = !state.mobileMenuOpen;\n    },\n    \n    // Reset UI state\n    resetUI: (state) => {\n      return {\n        ...initialState,\n        theme: state.theme,\n        soundEnabled: state.soundEnabled,\n      };\n    },\n  },\n});\n\nexport const {\n  // Modal actions\n  openModal,\n  closeModal,\n  updateModalData,\n  clearModal,\n  \n  // Toast actions\n  addToast,\n  removeToast,\n  clearAllToasts,\n  \n  // Global loading\n  setGlobalLoading,\n  \n  // Theme and preferences\n  setTheme,\n  setSoundEnabled,\n  \n  // Navigation\n  setCurrentPage,\n  \n  // Game UI\n  setShowPlayerList,\n  setShowScoreboard,\n  setShowGameControls,\n  \n  // Round-specific UI\n  setSelectedTopic,\n  incrementAnimationKey,\n  setAnimationKey,\n  \n  // Form management\n  setFormSubmitting,\n  setFormErrors,\n  clearFormErrors,\n  clearForm,\n  \n  // Sidebar and navigation\n  setSidebarOpen,\n  setMobileMenuOpen,\n  toggleSidebar,\n  toggleMobileMenu,\n  \n  // Reset\n  resetUI,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA;AACA,SAASA,WAAW,QAAuB,kBAAkB;AA6C7D,MAAMC,YAAqB,GAAG;EAC5BC,MAAM,EAAE,CAAC,CAAC;EACVC,MAAM,EAAE,EAAE;EACVC,aAAa,EAAE,KAAK;EACpBC,KAAK,EAAE,MAAM;EACbC,YAAY,EAAE,IAAI;EAClBC,WAAW,EAAE,EAAE;EACfC,YAAY,EAAE,EAAE;EAChBC,cAAc,EAAE,IAAI;EACpBC,cAAc,EAAE,IAAI;EACpBC,gBAAgB,EAAE,IAAI;EACtBC,aAAa,EAAE,EAAE;EACjBC,YAAY,EAAE,CAAC;EACfC,KAAK,EAAE,CAAC,CAAC;EACTC,WAAW,EAAE,KAAK;EAClBC,cAAc,EAAE;AAClB,CAAC;AAED,MAAMC,OAAO,GAAGjB,WAAW,CAAC;EAC1BkB,IAAI,EAAE,IAAI;EACVjB,YAAY;EACZkB,QAAQ,EAAE;IACR;IACAC,SAAS,EAAEA,CAACC,KAAK,EAAEC,MAA+D,KAAK;MACrFD,KAAK,CAACnB,MAAM,CAACoB,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,GAAG;QAChCC,MAAM,EAAE,IAAI;QACZC,IAAI,EAAEJ,MAAM,CAACC,OAAO,CAACG,IAAI;QACzBC,IAAI,EAAEL,MAAM,CAACC,OAAO,CAACI,IAAI,IAAI;MAC/B,CAAC;IACH,CAAC;IAEDC,UAAU,EAAEA,CAACP,KAAK,EAAEC,MAA6B,KAAK;MACpD,IAAID,KAAK,CAACnB,MAAM,CAACoB,MAAM,CAACC,OAAO,CAAC,EAAE;QAChCF,KAAK,CAACnB,MAAM,CAACoB,MAAM,CAACC,OAAO,CAAC,CAACE,MAAM,GAAG,KAAK;MAC7C;IACF,CAAC;IAEDI,eAAe,EAAEA,CAACR,KAAK,EAAEC,MAAgD,KAAK;MAC5E,IAAID,KAAK,CAACnB,MAAM,CAACoB,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,EAAE;QACnCH,KAAK,CAACnB,MAAM,CAACoB,MAAM,CAACC,OAAO,CAACC,EAAE,CAAC,CAACG,IAAI,GAAGL,MAAM,CAACC,OAAO,CAACI,IAAI;MAC5D;IACF,CAAC;IAEDG,UAAU,EAAEA,CAACT,KAAK,EAAEC,MAA6B,KAAK;MACpD,OAAOD,KAAK,CAACnB,MAAM,CAACoB,MAAM,CAACC,OAAO,CAAC;IACrC,CAAC;IAED;IACAQ,QAAQ,EAAEA,CAACV,KAAK,EAAEC,MAAoD,KAAK;MAAA,IAAAU,qBAAA,EAAAC,qBAAA;MACzE,MAAMC,KAAwB,GAAG;QAC/B,GAAGZ,MAAM,CAACC,OAAO;QACjBC,EAAE,EAAEW,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,GAAGC,IAAI,CAACC,MAAM,CAAC,CAAC,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;QACnEC,SAAS,GAAAT,qBAAA,GAAEV,MAAM,CAACC,OAAO,CAACkB,SAAS,cAAAT,qBAAA,cAAAA,qBAAA,GAAI,IAAI;QAC3CU,QAAQ,GAAAT,qBAAA,GAAEX,MAAM,CAACC,OAAO,CAACmB,QAAQ,cAAAT,qBAAA,cAAAA,qBAAA,GAAI;MACvC,CAAC;MACDZ,KAAK,CAAClB,MAAM,CAACwC,IAAI,CAACT,KAAK,CAAC;IAC1B,CAAC;IAEDU,WAAW,EAAEA,CAACvB,KAAK,EAAEC,MAA6B,KAAK;MACrDD,KAAK,CAAClB,MAAM,GAAGkB,KAAK,CAAClB,MAAM,CAAC0C,MAAM,CAACX,KAAK,IAAIA,KAAK,CAACV,EAAE,KAAKF,MAAM,CAACC,OAAO,CAAC;IAC1E,CAAC;IAEDuB,cAAc,EAAGzB,KAAK,IAAK;MACzBA,KAAK,CAAClB,MAAM,GAAG,EAAE;IACnB,CAAC;IAED;IACA4C,gBAAgB,EAAEA,CAAC1B,KAAK,EAAEC,MAA8B,KAAK;MAC3DD,KAAK,CAACjB,aAAa,GAAGkB,MAAM,CAACC,OAAO;IACtC,CAAC;IAED;IACAyB,QAAQ,EAAEA,CAAC3B,KAAK,EAAEC,MAAgD,KAAK;MACrED,KAAK,CAAChB,KAAK,GAAGiB,MAAM,CAACC,OAAO;MAC5B0B,YAAY,CAACC,OAAO,CAAC,OAAO,EAAE5B,MAAM,CAACC,OAAO,CAAC;IAC/C,CAAC;IAED4B,eAAe,EAAEA,CAAC9B,KAAK,EAAEC,MAA8B,KAAK;MAC1DD,KAAK,CAACf,YAAY,GAAGgB,MAAM,CAACC,OAAO;MACnC0B,YAAY,CAACC,OAAO,CAAC,cAAc,EAAE5B,MAAM,CAACC,OAAO,CAACc,QAAQ,CAAC,CAAC,CAAC;IACjE,CAAC;IAED;IACAe,cAAc,EAAEA,CAAC/B,KAAK,EAAEC,MAA6B,KAAK;MACxDD,KAAK,CAACb,YAAY,GAAGa,KAAK,CAACd,WAAW;MACtCc,KAAK,CAACd,WAAW,GAAGe,MAAM,CAACC,OAAO;IACpC,CAAC;IAED;IACA8B,iBAAiB,EAAEA,CAAChC,KAAK,EAAEC,MAA8B,KAAK;MAC5DD,KAAK,CAACZ,cAAc,GAAGa,MAAM,CAACC,OAAO;IACvC,CAAC;IAED+B,iBAAiB,EAAEA,CAACjC,KAAK,EAAEC,MAA8B,KAAK;MAC5DD,KAAK,CAACX,cAAc,GAAGY,MAAM,CAACC,OAAO;IACvC,CAAC;IAEDgC,mBAAmB,EAAEA,CAAClC,KAAK,EAAEC,MAA8B,KAAK;MAC9DD,KAAK,CAACV,gBAAgB,GAAGW,MAAM,CAACC,OAAO;IACzC,CAAC;IAED;IACAiC,gBAAgB,EAAEA,CAACnC,KAAK,EAAEC,MAA6B,KAAK;MAC1DD,KAAK,CAACT,aAAa,GAAGU,MAAM,CAACC,OAAO;IACtC,CAAC;IAEDkC,qBAAqB,EAAGpC,KAAK,IAAK;MAChCA,KAAK,CAACR,YAAY,IAAI,CAAC;IACzB,CAAC;IAED6C,eAAe,EAAEA,CAACrC,KAAK,EAAEC,MAA6B,KAAK;MACzDD,KAAK,CAACR,YAAY,GAAGS,MAAM,CAACC,OAAO;IACrC,CAAC;IAED;IACAoC,iBAAiB,EAAEA,CAACtC,KAAK,EAAEC,MAAgE,KAAK;MAC9F,IAAI,CAACD,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACqC,MAAM,CAAC,EAAE;QACvCvC,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACqC,MAAM,CAAC,GAAG;UAAEC,YAAY,EAAE,KAAK;UAAEC,MAAM,EAAE,CAAC;QAAE,CAAC;MAC1E;MACAzC,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACqC,MAAM,CAAC,CAACC,YAAY,GAAGvC,MAAM,CAACC,OAAO,CAACsC,YAAY;IAC/E,CAAC;IAEDE,aAAa,EAAEA,CAAC1C,KAAK,EAAEC,MAA8E,KAAK;MACxG,IAAI,CAACD,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACqC,MAAM,CAAC,EAAE;QACvCvC,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACqC,MAAM,CAAC,GAAG;UAAEC,YAAY,EAAE,KAAK;UAAEC,MAAM,EAAE,CAAC;QAAE,CAAC;MAC1E;MACAzC,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAACqC,MAAM,CAAC,CAACE,MAAM,GAAGxC,MAAM,CAACC,OAAO,CAACuC,MAAM;IACnE,CAAC;IAEDE,eAAe,EAAEA,CAAC3C,KAAK,EAAEC,MAA6B,KAAK;MACzD,IAAID,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAAC,EAAE;QAC/BF,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAAC,CAACuC,MAAM,GAAG,CAAC,CAAC;MACzC;IACF,CAAC;IAEDG,SAAS,EAAEA,CAAC5C,KAAK,EAAEC,MAA6B,KAAK;MACnD,OAAOD,KAAK,CAACP,KAAK,CAACQ,MAAM,CAACC,OAAO,CAAC;IACpC,CAAC;IAED;IACA2C,cAAc,EAAEA,CAAC7C,KAAK,EAAEC,MAA8B,KAAK;MACzDD,KAAK,CAACN,WAAW,GAAGO,MAAM,CAACC,OAAO;IACpC,CAAC;IAED4C,iBAAiB,EAAEA,CAAC9C,KAAK,EAAEC,MAA8B,KAAK;MAC5DD,KAAK,CAACL,cAAc,GAAGM,MAAM,CAACC,OAAO;IACvC,CAAC;IAED6C,aAAa,EAAG/C,KAAK,IAAK;MACxBA,KAAK,CAACN,WAAW,GAAG,CAACM,KAAK,CAACN,WAAW;IACxC,CAAC;IAEDsD,gBAAgB,EAAGhD,KAAK,IAAK;MAC3BA,KAAK,CAACL,cAAc,GAAG,CAACK,KAAK,CAACL,cAAc;IAC9C,CAAC;IAED;IACAsD,OAAO,EAAGjD,KAAK,IAAK;MAClB,OAAO;QACL,GAAGpB,YAAY;QACfI,KAAK,EAAEgB,KAAK,CAAChB,KAAK;QAClBC,YAAY,EAAEe,KAAK,CAACf;MACtB,CAAC;IACH;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACX;EACAc,SAAS;EACTQ,UAAU;EACVC,eAAe;EACfC,UAAU;EAEV;EACAC,QAAQ;EACRa,WAAW;EACXE,cAAc;EAEd;EACAC,gBAAgB;EAEhB;EACAC,QAAQ;EACRG,eAAe;EAEf;EACAC,cAAc;EAEd;EACAC,iBAAiB;EACjBC,iBAAiB;EACjBC,mBAAmB;EAEnB;EACAC,gBAAgB;EAChBC,qBAAqB;EACrBC,eAAe;EAEf;EACAC,iBAAiB;EACjBI,aAAa;EACbC,eAAe;EACfC,SAAS;EAET;EACAC,cAAc;EACdC,iBAAiB;EACjBC,aAAa;EACbC,gBAAgB;EAEhB;EACAC;AACF,CAAC,GAAGrD,OAAO,CAACsD,OAAO;AAEnB,eAAetD,OAAO,CAACuD,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}