{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Documents\\\\New HTM\\\\htm_fe\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$(),\n  _s2 = $RefreshSig$(),\n  _s3 = $RefreshSig$(),\n  _s4 = $RefreshSig$();\nimport './App.css';\nimport React, { Suspense } from 'react';\nimport { Routes, Route, useSearchParams } from \"react-router-dom\";\nimport CreateRoom from './pages/Host/Room/CreateRoom';\n// PHASE 1: New Redux Provider\nimport ReduxProvider from './app/store/providers/ReduxProvider';\n// Legacy context providers (will be migrated in later phases)\nimport { AxiosAuthProvider } from './context/authContext';\nimport { PlayerProvider } from './context/playerContext';\nimport { HostProvider } from './context/hostContext';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { TimeStartProvider } from './context/timeListenerContext';\nimport { SoundProvider } from './context/soundContext';\nimport FallBack from './components/ui/FallBack';\nimport ProtectedRoute from './routes/ProtectedRoute';\nimport { ToastContainer } from 'react-toastify';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport 'react-toastify/dist/ReactToastify.css';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient();\nconst Home = /*#__PURE__*/React.lazy(_c = () => import('./pages/Home/Home'));\n_c2 = Home;\nconst UserRound1 = /*#__PURE__*/React.lazy(_c3 = () => import('./pages/User/UserRound1'));\n_c4 = UserRound1;\nconst UserRound2 = /*#__PURE__*/React.lazy(_c5 = () => import('./pages/User/Round2/UserRound2'));\n_c6 = UserRound2;\nconst UserRound3 = /*#__PURE__*/React.lazy(_c7 = () => import('./pages/User/Round3/UserRound3'));\n_c8 = UserRound3;\nconst UserRound4 = /*#__PURE__*/React.lazy(_c9 = () => import('./pages/User/Round4/UserRound4'));\n_c10 = UserRound4;\nconst UserRoundTurn = /*#__PURE__*/React.lazy(_c11 = () => import('./pages/User/RoundTurn/UserRoundTurn'));\n_c12 = UserRoundTurn;\nconst HostRound1 = /*#__PURE__*/React.lazy(_c13 = () => import('./pages/Host/Management/HostRound1'));\n_c14 = HostRound1;\nconst HostRound2 = /*#__PURE__*/React.lazy(_c15 = () => import('./pages/Host/Management/HostRound2'));\n_c16 = HostRound2;\nconst HostRound3 = /*#__PURE__*/React.lazy(_c17 = () => import('./pages/Host/Management/HostRound3'));\n_c18 = HostRound3;\nconst HostRound4 = /*#__PURE__*/React.lazy(_c19 = () => import('./pages/Host/Management/HostRound4'));\n_c20 = HostRound4;\nconst HostRoundTurn = /*#__PURE__*/React.lazy(_c21 = () => import('./pages/Host/Management/HostRoundTurn'));\n_c22 = HostRoundTurn;\nconst Login = /*#__PURE__*/React.lazy(_c23 = () => import('./pages/Login/Login'));\n_c24 = Login;\nconst JoinRoom = /*#__PURE__*/React.lazy(_c25 = () => import('./pages/JoinRoom/JoinRoom'));\n_c26 = JoinRoom;\nconst SpectatorJoin = /*#__PURE__*/React.lazy(_c27 = () => import('./pages/Spectator/SpectatorJoin'));\n_c28 = SpectatorJoin;\nconst InfoForm = /*#__PURE__*/React.lazy(_c29 = () => import('./pages/User/InformationForm/InformationForm'));\n_c30 = InfoForm;\nconst HostFinalScore = /*#__PURE__*/React.lazy(_c31 = () => import('./pages/FinalScore/HostFinalScore'));\n_c32 = HostFinalScore;\nconst PlayerFinalScore = /*#__PURE__*/React.lazy(_c33 = () => import('./pages/FinalScore/PlayerFinalScore'));\n_c34 = PlayerFinalScore;\nconst Dashboard = /*#__PURE__*/React.lazy(_c35 = () => import('./pages/Host/Test/Dashboard'));\n_c36 = Dashboard;\nfunction PlayComponent() {\n  _s();\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n  if (round === \"1\") return /*#__PURE__*/_jsxDEV(UserRound1, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 52,\n    columnNumber: 29\n  }, this);\n  if (round === \"2\") return /*#__PURE__*/_jsxDEV(UserRound2, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 53,\n    columnNumber: 29\n  }, this);\n  if (round === \"3\") return /*#__PURE__*/_jsxDEV(UserRound3, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 54,\n    columnNumber: 29\n  }, this);\n  if (round === \"4\") return /*#__PURE__*/_jsxDEV(UserRound4, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 55,\n    columnNumber: 29\n  }, this);\n  if (round === \"turn\") return /*#__PURE__*/_jsxDEV(UserRoundTurn, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 56,\n    columnNumber: 32\n  }, this);\n  if (round === \"final\") return /*#__PURE__*/_jsxDEV(PlayerFinalScore, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 57,\n    columnNumber: 33\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center text-red-500\",\n    children: \"Round kh\\xF4ng h\\u1EE3p l\\u1EC7!\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 59,\n    columnNumber: 10\n  }, this);\n}\n_s(PlayComponent, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c37 = PlayComponent;\nfunction HostComponent() {\n  _s2();\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n  if (round === \"1\") return /*#__PURE__*/_jsxDEV(HostRound1, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 66,\n    columnNumber: 29\n  }, this);\n  if (round === \"2\") return /*#__PURE__*/_jsxDEV(HostRound2, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 67,\n    columnNumber: 29\n  }, this);\n  if (round === \"3\") return /*#__PURE__*/_jsxDEV(HostRound3, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 68,\n    columnNumber: 29\n  }, this);\n  if (round === \"4\") return /*#__PURE__*/_jsxDEV(HostRound4, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 69,\n    columnNumber: 29\n  }, this);\n  if (round === \"turn\") return /*#__PURE__*/_jsxDEV(HostRoundTurn, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 70,\n    columnNumber: 32\n  }, this);\n  if (round === \"final\") return /*#__PURE__*/_jsxDEV(HostFinalScore, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 71,\n    columnNumber: 33\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center text-red-500\",\n    children: \"Round kh\\xF4ng h\\u1EE3p l\\u1EC7!\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 10\n  }, this);\n}\n_s2(HostComponent, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c38 = HostComponent;\nfunction SpectatorComponent() {\n  _s3();\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n  if (round === \"1\") return /*#__PURE__*/_jsxDEV(UserRound1, {\n    isSpectator: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 80,\n    columnNumber: 29\n  }, this);\n  if (round === \"2\") return /*#__PURE__*/_jsxDEV(UserRound2, {\n    isSpectator: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 81,\n    columnNumber: 29\n  }, this);\n  if (round === \"3\") return /*#__PURE__*/_jsxDEV(UserRound3, {\n    isSpectator: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 82,\n    columnNumber: 29\n  }, this);\n  if (round === \"4\") return /*#__PURE__*/_jsxDEV(UserRound4, {\n    isSpectator: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 29\n  }, this);\n  if (round === \"turn\") return /*#__PURE__*/_jsxDEV(UserRoundTurn, {\n    isSpectator: true\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 84,\n    columnNumber: 32\n  }, this);\n  if (round === \"final\") return /*#__PURE__*/_jsxDEV(PlayerFinalScore, {}, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 85,\n    columnNumber: 33\n  }, this);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center text-red-500\",\n    children: \"Round kh\\xF4ng h\\u1EE3p l\\u1EC7!\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 87,\n    columnNumber: 10\n  }, this);\n}\n_s3(SpectatorComponent, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c39 = SpectatorComponent;\nfunction App() {\n  _s4();\n  const [searchParams] = useSearchParams();\n  const roundName = `Round${searchParams.get(\"round\") || \"1\"}`;\n  const roomId = searchParams.get(\"roomId\") || \"\";\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: /*#__PURE__*/_jsxDEV(ReduxProvider, {\n      children: /*#__PURE__*/_jsxDEV(PlayerProvider, {\n        children: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n          fallback: /*#__PURE__*/_jsxDEV(FallBack, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 100,\n            columnNumber: 36\n          }, this),\n          children: /*#__PURE__*/_jsxDEV(Suspense, {\n            fallback: /*#__PURE__*/_jsxDEV(FallBack, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 101,\n              columnNumber: 33\n            }, this),\n            children: [/*#__PURE__*/_jsxDEV(QueryClientProvider, {\n              client: queryClient,\n              children: /*#__PURE__*/_jsxDEV(TimeStartProvider, {\n                roomId: roomId,\n                children: /*#__PURE__*/_jsxDEV(SoundProvider, {\n                  children: /*#__PURE__*/_jsxDEV(HostProvider, {\n                    children: /*#__PURE__*/_jsxDEV(Routes, {\n                      children: [/*#__PURE__*/_jsxDEV(Route, {\n                        path: \"*\",\n                        element: /*#__PURE__*/_jsxDEV(Routes, {\n                          children: [/*#__PURE__*/_jsxDEV(Route, {\n                            path: \"/\",\n                            element: /*#__PURE__*/_jsxDEV(Home, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 113,\n                              columnNumber: 54\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 113,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(Route, {\n                            path: \"/join\",\n                            element: /*#__PURE__*/_jsxDEV(JoinRoom, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 114,\n                              columnNumber: 58\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 114,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(Route, {\n                            path: \"/spectatorJoin\",\n                            element: /*#__PURE__*/_jsxDEV(SpectatorJoin, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 115,\n                              columnNumber: 67\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 115,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(Route, {\n                            path: \"/play\",\n                            element: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n                              onRetry: () => window.location.reload(),\n                              children: /*#__PURE__*/_jsxDEV(PlayComponent, {}, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 118,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 117,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 116,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(Route, {\n                            path: \"/login\",\n                            element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 121,\n                              columnNumber: 59\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 121,\n                            columnNumber: 29\n                          }, this), /*#__PURE__*/_jsxDEV(Route, {\n                            path: \"/user/info\",\n                            element: /*#__PURE__*/_jsxDEV(InfoForm, {}, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 122,\n                              columnNumber: 63\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 122,\n                            columnNumber: 29\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 112,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 108,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/host/*\",\n                        element: /*#__PURE__*/_jsxDEV(HostProvider, {\n                          children: /*#__PURE__*/_jsxDEV(AxiosAuthProvider, {\n                            children: /*#__PURE__*/_jsxDEV(Routes, {\n                              children: [/*#__PURE__*/_jsxDEV(Route, {\n                                path: \"/login\",\n                                element: /*#__PURE__*/_jsxDEV(Login, {}, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 135,\n                                  columnNumber: 63\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 135,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                                path: \"dashboard\",\n                                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                                  element: /*#__PURE__*/_jsxDEV(Dashboard, {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 136,\n                                    columnNumber: 91\n                                  }, this),\n                                  requireAccessToken: false,\n                                  requireHost: true\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 136,\n                                  columnNumber: 66\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 136,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                                path: \"create_room\",\n                                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                                  element: /*#__PURE__*/_jsxDEV(CreateRoom, {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 137,\n                                    columnNumber: 93\n                                  }, this),\n                                  requireAccessToken: false,\n                                  requireHost: true\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 137,\n                                  columnNumber: 68\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 137,\n                                columnNumber: 33\n                              }, this), /*#__PURE__*/_jsxDEV(Route, {\n                                path: \"\",\n                                element: /*#__PURE__*/_jsxDEV(ProtectedRoute, {\n                                  element: /*#__PURE__*/_jsxDEV(HostComponent, {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 138,\n                                    columnNumber: 82\n                                  }, this),\n                                  requireAccessToken: true\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 138,\n                                  columnNumber: 57\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 138,\n                                columnNumber: 33\n                              }, this)]\n                            }, void 0, true, {\n                              fileName: _jsxFileName,\n                              lineNumber: 134,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 133,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 132,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 129,\n                        columnNumber: 23\n                      }, this), /*#__PURE__*/_jsxDEV(Route, {\n                        path: \"/spectator/*\",\n                        element: /*#__PURE__*/_jsxDEV(HostProvider, {\n                          children: /*#__PURE__*/_jsxDEV(AxiosAuthProvider, {\n                            children: /*#__PURE__*/_jsxDEV(Routes, {\n                              children: /*#__PURE__*/_jsxDEV(Route, {\n                                path: \"\",\n                                element: /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n                                  onRetry: () => window.location.reload(),\n                                  children: /*#__PURE__*/_jsxDEV(SpectatorComponent, {}, void 0, false, {\n                                    fileName: _jsxFileName,\n                                    lineNumber: 154,\n                                    columnNumber: 37\n                                  }, this)\n                                }, void 0, false, {\n                                  fileName: _jsxFileName,\n                                  lineNumber: 153,\n                                  columnNumber: 35\n                                }, this)\n                              }, void 0, false, {\n                                fileName: _jsxFileName,\n                                lineNumber: 152,\n                                columnNumber: 33\n                              }, this)\n                            }, void 0, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 151,\n                              columnNumber: 31\n                            }, this)\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 150,\n                            columnNumber: 29\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 149,\n                          columnNumber: 27\n                        }, this)\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 146,\n                        columnNumber: 23\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 106,\n                      columnNumber: 21\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 105,\n                    columnNumber: 21\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 104,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 103,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ToastContainer, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 170,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 101,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 100,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 99,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 97,\n      columnNumber: 7\n    }, this)\n  }, void 0, false);\n}\n_s4(App, \"HWxNQEGJGSlsPJ3ubBB3081mtng=\", false, function () {\n  return [useSearchParams];\n});\n_c40 = App;\nexport default App;\nvar _c, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21, _c22, _c23, _c24, _c25, _c26, _c27, _c28, _c29, _c30, _c31, _c32, _c33, _c34, _c35, _c36, _c37, _c38, _c39, _c40;\n$RefreshReg$(_c, \"Home$React.lazy\");\n$RefreshReg$(_c2, \"Home\");\n$RefreshReg$(_c3, \"UserRound1$React.lazy\");\n$RefreshReg$(_c4, \"UserRound1\");\n$RefreshReg$(_c5, \"UserRound2$React.lazy\");\n$RefreshReg$(_c6, \"UserRound2\");\n$RefreshReg$(_c7, \"UserRound3$React.lazy\");\n$RefreshReg$(_c8, \"UserRound3\");\n$RefreshReg$(_c9, \"UserRound4$React.lazy\");\n$RefreshReg$(_c10, \"UserRound4\");\n$RefreshReg$(_c11, \"UserRoundTurn$React.lazy\");\n$RefreshReg$(_c12, \"UserRoundTurn\");\n$RefreshReg$(_c13, \"HostRound1$React.lazy\");\n$RefreshReg$(_c14, \"HostRound1\");\n$RefreshReg$(_c15, \"HostRound2$React.lazy\");\n$RefreshReg$(_c16, \"HostRound2\");\n$RefreshReg$(_c17, \"HostRound3$React.lazy\");\n$RefreshReg$(_c18, \"HostRound3\");\n$RefreshReg$(_c19, \"HostRound4$React.lazy\");\n$RefreshReg$(_c20, \"HostRound4\");\n$RefreshReg$(_c21, \"HostRoundTurn$React.lazy\");\n$RefreshReg$(_c22, \"HostRoundTurn\");\n$RefreshReg$(_c23, \"Login$React.lazy\");\n$RefreshReg$(_c24, \"Login\");\n$RefreshReg$(_c25, \"JoinRoom$React.lazy\");\n$RefreshReg$(_c26, \"JoinRoom\");\n$RefreshReg$(_c27, \"SpectatorJoin$React.lazy\");\n$RefreshReg$(_c28, \"SpectatorJoin\");\n$RefreshReg$(_c29, \"InfoForm$React.lazy\");\n$RefreshReg$(_c30, \"InfoForm\");\n$RefreshReg$(_c31, \"HostFinalScore$React.lazy\");\n$RefreshReg$(_c32, \"HostFinalScore\");\n$RefreshReg$(_c33, \"PlayerFinalScore$React.lazy\");\n$RefreshReg$(_c34, \"PlayerFinalScore\");\n$RefreshReg$(_c35, \"Dashboard$React.lazy\");\n$RefreshReg$(_c36, \"Dashboard\");\n$RefreshReg$(_c37, \"PlayComponent\");\n$RefreshReg$(_c38, \"HostComponent\");\n$RefreshReg$(_c39, \"SpectatorComponent\");\n$RefreshReg$(_c40, \"App\");", "map": {"version": 3, "names": ["React", "Suspense", "Routes", "Route", "useSearchParams", "CreateRoom", "ReduxProvider", "AxiosAuthProvider", "<PERSON><PERSON><PERSON><PERSON>", "HostProvider", "QueryClient", "QueryClientProvider", "TimeStartProvider", "SoundProvider", "FallBack", "ProtectedRoute", "ToastContainer", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "queryClient", "Home", "lazy", "_c", "_c2", "UserRound1", "_c3", "_c4", "UserRound2", "_c5", "_c6", "UserRound3", "_c7", "_c8", "UserRound4", "_c9", "_c10", "UserRoundTurn", "_c11", "_c12", "HostRound1", "_c13", "_c14", "HostRound2", "_c15", "_c16", "HostRound3", "_c17", "_c18", "HostRound4", "_c19", "_c20", "HostRoundTurn", "_c21", "_c22", "<PERSON><PERSON>", "_c23", "_c24", "<PERSON><PERSON><PERSON><PERSON>", "_c25", "_c26", "Spectator<PERSON><PERSON>n", "_c27", "_c28", "InfoForm", "_c29", "_c30", "HostFinalScore", "_c31", "_c32", "PlayerFinalScore", "_c33", "_c34", "Dashboard", "_c35", "_c36", "PlayComponent", "_s", "searchParams", "round", "get", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "_c37", "HostComponent", "_s2", "_c38", "SpectatorComponent", "_s3", "isSpectator", "_c39", "App", "_s4", "roundName", "roomId", "fallback", "client", "path", "element", "onRetry", "window", "location", "reload", "requireAccessToken", "requireHost", "_c40", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/App.tsx"], "sourcesContent": ["import './App.css';\nimport React, { Suspense, useEffect, useRef } from 'react';\nimport { Routes, Route, useLocation, useSearchParams } from \"react-router-dom\";\nimport CreateRoom from './pages/Host/Room/CreateRoom';\nimport LoadingSpinner from './layouts/Loading/LoadingSpinner';\n// PHASE 1: New Redux Provider\nimport ReduxProvider from './app/store/providers/ReduxProvider';\n// Legacy context providers (will be migrated in later phases)\nimport { AxiosAuthProvider } from './context/authContext';\nimport { PlayerProvider } from './context/playerContext';\nimport { HostProvider } from './context/hostContext';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { TimeStartProvider } from './context/timeListenerContext';\nimport { SoundProvider } from './context/soundContext';\nimport FallBack from './components/ui/FallBack';\nimport withRoleCheck from './routes/ProtectedRoute';\nimport ProtectedRoute from './routes/ProtectedRoute';\nimport { ToastContainer } from 'react-toastify';\nimport ErrorBoundary from './components/ErrorBoundary';\nimport 'react-toastify/dist/ReactToastify.css';\n\n\nconst queryClient = new QueryClient();\n\nconst Home = React.lazy(() => import('./pages/Home/Home'));\n\nconst UserRound1 = React.lazy(() => import('./pages/User/UserRound1'))\nconst UserRound2 = React.lazy(() => import('./pages/User/Round2/UserRound2'));\nconst UserRound3 = React.lazy(() => import('./pages/User/Round3/UserRound3'));\nconst UserRound4 = React.lazy(() => import('./pages/User/Round4/UserRound4'));\nconst UserRoundTurn = React.lazy(() => import('./pages/User/RoundTurn/UserRoundTurn'));\n\nconst HostRound1 = React.lazy(() => import('./pages/Host/Management/HostRound1'));\nconst HostRound2 = React.lazy(() => import('./pages/Host/Management/HostRound2'));\nconst HostRound3 = React.lazy(() => import('./pages/Host/Management/HostRound3'));\nconst HostRound4 = React.lazy(() => import('./pages/Host/Management/HostRound4'));\nconst HostRoundTurn = React.lazy(() => import('./pages/Host/Management/HostRoundTurn'));\n\nconst Login = React.lazy(() => import('./pages/Login/Login'))\nconst JoinRoom = React.lazy(() => import('./pages/JoinRoom/JoinRoom'))\nconst SpectatorJoin = React.lazy(() => import('./pages/Spectator/SpectatorJoin'))\nconst InfoForm = React.lazy(() => import('./pages/User/InformationForm/InformationForm'))\nconst HostFinalScore = React.lazy(() => import('./pages/FinalScore/HostFinalScore'));\nconst PlayerFinalScore = React.lazy(() => import('./pages/FinalScore/PlayerFinalScore'));\nconst Dashboard = React.lazy(() => import('./pages/Host/Test/Dashboard'))\n\nfunction PlayComponent() {\n  const [searchParams] = useSearchParams();\n\n  const round = searchParams.get(\"round\") || \"1\";\n\n  if (round === \"1\") return <UserRound1 />;\n  if (round === \"2\") return <UserRound2 />;\n  if (round === \"3\") return <UserRound3 />;\n  if (round === \"4\") return <UserRound4 />;\n  if (round === \"turn\") return <UserRoundTurn />;\n  if (round === \"final\") return <PlayerFinalScore />;\n\n  return <div className=\"text-center text-red-500\">Round không hợp lệ!</div>;\n}\n\nfunction HostComponent() {\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n\n  if (round === \"1\") return <HostRound1 />;\n  if (round === \"2\") return <HostRound2 />;\n  if (round === \"3\") return <HostRound3 />;\n  if (round === \"4\") return <HostRound4 />;\n  if (round === \"turn\") return <HostRoundTurn />;\n  if (round === \"final\") return <HostFinalScore />;\n\n  return <div className=\"text-center text-red-500\">Round không hợp lệ!</div>;\n}\n\nfunction SpectatorComponent() {\n  const [searchParams] = useSearchParams();\n  const round = searchParams.get(\"round\") || \"1\";\n\n  if (round === \"1\") return <UserRound1 isSpectator={true} />;\n  if (round === \"2\") return <UserRound2 isSpectator={true} />;\n  if (round === \"3\") return <UserRound3 isSpectator={true} />;\n  if (round === \"4\") return <UserRound4 isSpectator={true} />;\n  if (round === \"turn\") return <UserRoundTurn isSpectator={true} />;\n  if (round === \"final\") return <PlayerFinalScore />;\n\n  return <div className=\"text-center text-red-500\">Round không hợp lệ!</div>;\n}\n\nfunction App() {\n  const [searchParams] = useSearchParams();\n  const roundName = `Round${searchParams.get(\"round\") || \"1\"}`\n  const roomId = searchParams.get(\"roomId\") || \"\"\n  return (\n    <>\n      {/* PHASE 1: Redux Provider wraps everything */}\n      <ReduxProvider>\n        {/* Legacy providers - will be migrated in later phases */}\n        <PlayerProvider>\n          <ErrorBoundary fallback={<FallBack />}>\n            <Suspense fallback={<FallBack />}>\n              <QueryClientProvider client={queryClient}>\n                <TimeStartProvider roomId={roomId}>\n                  <SoundProvider>\n                    <HostProvider>\n                    <Routes>\n                      {/* Public Routes */}\n                      <Route\n                        path=\"*\"\n                        element={\n\n                          <Routes>\n                            <Route path=\"/\" element={<Home />} />\n                            <Route path=\"/join\" element={<JoinRoom />} />\n                            <Route path=\"/spectatorJoin\" element={<SpectatorJoin />} />\n                            <Route path=\"/play\" element={\n                              <ErrorBoundary onRetry={() => window.location.reload()}>\n                                <PlayComponent />\n                              </ErrorBoundary>\n                            } />\n                            <Route path=\"/login\" element={<Login />} />\n                            <Route path=\"/user/info\" element={<InfoForm />} />\n                          </Routes>\n\n\n                        }\n                      />\n                      {/* Host Routes */}\n                      <Route\n                        path=\"/host/*\"\n                        element={\n                          <HostProvider>\n                            <AxiosAuthProvider>\n                              <Routes>\n                                <Route path=\"/login\" element={<Login />} />\n                                <Route path=\"dashboard\" element={<ProtectedRoute element={<Dashboard />} requireAccessToken={false} requireHost={true} />} />\n                                <Route path=\"create_room\" element={<ProtectedRoute element={<CreateRoom />} requireAccessToken={false} requireHost={true} />} />\n                                <Route path=\"\" element={<ProtectedRoute element={<HostComponent />} requireAccessToken={true} />} />\n                              </Routes>\n                            </AxiosAuthProvider>\n                          </HostProvider>\n\n                        }\n                      />\n\n                      <Route\n                        path=\"/spectator/*\"\n                        element={\n                          <HostProvider>\n                            <AxiosAuthProvider>\n                              <Routes>\n                                <Route path=\"\" element={\n                                  <ErrorBoundary onRetry={() => window.location.reload()}>\n                                    <SpectatorComponent />\n                                  </ErrorBoundary>\n                                } />\n                              </Routes>\n                            </AxiosAuthProvider>\n                          </HostProvider>\n\n                        }\n                      />\n                    </Routes>\n                  </HostProvider>\n\n                    </SoundProvider>\n\n                  </TimeStartProvider>\n                </QueryClientProvider>\n                <ToastContainer />\n              </Suspense>\n            </ErrorBoundary>\n          </PlayerProvider>\n        </ReduxProvider>\n      </>\n    );\n}\n\nexport default App;\n"], "mappings": ";;;;;AAAA,OAAO,WAAW;AAClB,OAAOA,KAAK,IAAIC,QAAQ,QAA2B,OAAO;AAC1D,SAASC,MAAM,EAAEC,KAAK,EAAeC,eAAe,QAAQ,kBAAkB;AAC9E,OAAOC,UAAU,MAAM,8BAA8B;AAErD;AACA,OAAOC,aAAa,MAAM,qCAAqC;AAC/D;AACA,SAASC,iBAAiB,QAAQ,uBAAuB;AACzD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,aAAa,QAAQ,wBAAwB;AACtD,OAAOC,QAAQ,MAAM,0BAA0B;AAE/C,OAAOC,cAAc,MAAM,yBAAyB;AACpD,SAASC,cAAc,QAAQ,gBAAgB;AAC/C,OAAOC,aAAa,MAAM,4BAA4B;AACtD,OAAO,uCAAuC;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAG/C,MAAMC,WAAW,GAAG,IAAIZ,WAAW,CAAC,CAAC;AAErC,MAAMa,IAAI,gBAAGvB,KAAK,CAACwB,IAAI,CAAAC,EAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mBAAmB,CAAC,CAAC;AAACC,GAAA,GAArDH,IAAI;AAEV,MAAMI,UAAU,gBAAG3B,KAAK,CAACwB,IAAI,CAAAI,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,yBAAyB,CAAC,CAAC;AAAAC,GAAA,GAAhEF,UAAU;AAChB,MAAMG,UAAU,gBAAG9B,KAAK,CAACwB,IAAI,CAAAO,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAACC,GAAA,GAAxEF,UAAU;AAChB,MAAMG,UAAU,gBAAGjC,KAAK,CAACwB,IAAI,CAAAU,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAACC,GAAA,GAAxEF,UAAU;AAChB,MAAMG,UAAU,gBAAGpC,KAAK,CAACwB,IAAI,CAAAa,GAAA,GAACA,CAAA,KAAM,MAAM,CAAC,gCAAgC,CAAC,CAAC;AAACC,IAAA,GAAxEF,UAAU;AAChB,MAAMG,aAAa,gBAAGvC,KAAK,CAACwB,IAAI,CAAAgB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,sCAAsC,CAAC,CAAC;AAACC,IAAA,GAAjFF,aAAa;AAEnB,MAAMG,UAAU,gBAAG1C,KAAK,CAACwB,IAAI,CAAAmB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAACC,IAAA,GAA5EF,UAAU;AAChB,MAAMG,UAAU,gBAAG7C,KAAK,CAACwB,IAAI,CAAAsB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAACC,IAAA,GAA5EF,UAAU;AAChB,MAAMG,UAAU,gBAAGhD,KAAK,CAACwB,IAAI,CAAAyB,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAACC,IAAA,GAA5EF,UAAU;AAChB,MAAMG,UAAU,gBAAGnD,KAAK,CAACwB,IAAI,CAAA4B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,oCAAoC,CAAC,CAAC;AAACC,IAAA,GAA5EF,UAAU;AAChB,MAAMG,aAAa,gBAAGtD,KAAK,CAACwB,IAAI,CAAA+B,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAAC;AAACC,IAAA,GAAlFF,aAAa;AAEnB,MAAMG,KAAK,gBAAGzD,KAAK,CAACwB,IAAI,CAAAkC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qBAAqB,CAAC,CAAC;AAAAC,IAAA,GAAvDF,KAAK;AACX,MAAMG,QAAQ,gBAAG5D,KAAK,CAACwB,IAAI,CAAAqC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,2BAA2B,CAAC,CAAC;AAAAC,IAAA,GAAhEF,QAAQ;AACd,MAAMG,aAAa,gBAAG/D,KAAK,CAACwB,IAAI,CAAAwC,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,iCAAiC,CAAC,CAAC;AAAAC,IAAA,GAA3EF,aAAa;AACnB,MAAMG,QAAQ,gBAAGlE,KAAK,CAACwB,IAAI,CAAA2C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,8CAA8C,CAAC,CAAC;AAAAC,IAAA,GAAnFF,QAAQ;AACd,MAAMG,cAAc,gBAAGrE,KAAK,CAACwB,IAAI,CAAA8C,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAAC;AAACC,IAAA,GAA/EF,cAAc;AACpB,MAAMG,gBAAgB,gBAAGxE,KAAK,CAACwB,IAAI,CAAAiD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,qCAAqC,CAAC,CAAC;AAACC,IAAA,GAAnFF,gBAAgB;AACtB,MAAMG,SAAS,gBAAG3E,KAAK,CAACwB,IAAI,CAAAoD,IAAA,GAACA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAAC;AAAAC,IAAA,GAAnEF,SAAS;AAEf,SAASG,aAAaA,CAAA,EAAG;EAAAC,EAAA;EACvB,MAAM,CAACC,YAAY,CAAC,GAAG5E,eAAe,CAAC,CAAC;EAExC,MAAM6E,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EAE9C,IAAID,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACQ,UAAU;IAAAwD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACW,UAAU;IAAAqD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACc,UAAU;IAAAkD,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACiB,UAAU;IAAA+C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,MAAM,EAAE,oBAAO9D,OAAA,CAACoB,aAAa;IAAA4C,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC9C,IAAIL,KAAK,KAAK,OAAO,EAAE,oBAAO9D,OAAA,CAACqD,gBAAgB;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAElD,oBAAOnE,OAAA;IAAKoE,SAAS,EAAC,0BAA0B;IAAAC,QAAA,EAAC;EAAmB;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5E;AAACP,EAAA,CAbQD,aAAa;EAAA,QACG1E,eAAe;AAAA;AAAAqF,IAAA,GAD/BX,aAAa;AAetB,SAASY,aAAaA,CAAA,EAAG;EAAAC,GAAA;EACvB,MAAM,CAACX,YAAY,CAAC,GAAG5E,eAAe,CAAC,CAAC;EACxC,MAAM6E,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EAE9C,IAAID,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACuB,UAAU;IAAAyC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAAC0B,UAAU;IAAAsC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAAC6B,UAAU;IAAAmC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACgC,UAAU;IAAAgC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACxC,IAAIL,KAAK,KAAK,MAAM,EAAE,oBAAO9D,OAAA,CAACmC,aAAa;IAAA6B,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC9C,IAAIL,KAAK,KAAK,OAAO,EAAE,oBAAO9D,OAAA,CAACkD,cAAc;IAAAc,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAEhD,oBAAOnE,OAAA;IAAKoE,SAAS,EAAC,0BAA0B;IAAAC,QAAA,EAAC;EAAmB;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5E;AAACK,GAAA,CAZQD,aAAa;EAAA,QACGtF,eAAe;AAAA;AAAAwF,IAAA,GAD/BF,aAAa;AActB,SAASG,kBAAkBA,CAAA,EAAG;EAAAC,GAAA;EAC5B,MAAM,CAACd,YAAY,CAAC,GAAG5E,eAAe,CAAC,CAAC;EACxC,MAAM6E,KAAK,GAAGD,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG;EAE9C,IAAID,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACQ,UAAU;IAACoE,WAAW,EAAE;EAAK;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3D,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACW,UAAU;IAACiE,WAAW,EAAE;EAAK;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3D,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACc,UAAU;IAAC8D,WAAW,EAAE;EAAK;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3D,IAAIL,KAAK,KAAK,GAAG,EAAE,oBAAO9D,OAAA,CAACiB,UAAU;IAAC2D,WAAW,EAAE;EAAK;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAC3D,IAAIL,KAAK,KAAK,MAAM,EAAE,oBAAO9D,OAAA,CAACoB,aAAa;IAACwD,WAAW,EAAE;EAAK;IAAAZ,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EACjE,IAAIL,KAAK,KAAK,OAAO,EAAE,oBAAO9D,OAAA,CAACqD,gBAAgB;IAAAW,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;EAElD,oBAAOnE,OAAA;IAAKoE,SAAS,EAAC,0BAA0B;IAAAC,QAAA,EAAC;EAAmB;IAAAL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAK,CAAC;AAC5E;AAACQ,GAAA,CAZQD,kBAAkB;EAAA,QACFzF,eAAe;AAAA;AAAA4F,IAAA,GAD/BH,kBAAkB;AAc3B,SAASI,GAAGA,CAAA,EAAG;EAAAC,GAAA;EACb,MAAM,CAAClB,YAAY,CAAC,GAAG5E,eAAe,CAAC,CAAC;EACxC,MAAM+F,SAAS,GAAG,QAAQnB,YAAY,CAACE,GAAG,CAAC,OAAO,CAAC,IAAI,GAAG,EAAE;EAC5D,MAAMkB,MAAM,GAAGpB,YAAY,CAACE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC/C,oBACE/D,OAAA,CAAAE,SAAA;IAAAmE,QAAA,eAEErE,OAAA,CAACb,aAAa;MAAAkF,QAAA,eAEZrE,OAAA,CAACX,cAAc;QAAAgF,QAAA,eACbrE,OAAA,CAACF,aAAa;UAACoF,QAAQ,eAAElF,OAAA,CAACL,QAAQ;YAAAqE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAAE,QAAA,eACpCrE,OAAA,CAAClB,QAAQ;YAACoG,QAAQ,eAAElF,OAAA,CAACL,QAAQ;cAAAqE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAAAE,QAAA,gBAC/BrE,OAAA,CAACR,mBAAmB;cAAC2F,MAAM,EAAEhF,WAAY;cAAAkE,QAAA,eACvCrE,OAAA,CAACP,iBAAiB;gBAACwF,MAAM,EAAEA,MAAO;gBAAAZ,QAAA,eAChCrE,OAAA,CAACN,aAAa;kBAAA2E,QAAA,eACZrE,OAAA,CAACV,YAAY;oBAAA+E,QAAA,eACbrE,OAAA,CAACjB,MAAM;sBAAAsF,QAAA,gBAELrE,OAAA,CAAChB,KAAK;wBACJoG,IAAI,EAAC,GAAG;wBACRC,OAAO,eAELrF,OAAA,CAACjB,MAAM;0BAAAsF,QAAA,gBACLrE,OAAA,CAAChB,KAAK;4BAACoG,IAAI,EAAC,GAAG;4BAACC,OAAO,eAAErF,OAAA,CAACI,IAAI;8BAAA4D,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACrCnE,OAAA,CAAChB,KAAK;4BAACoG,IAAI,EAAC,OAAO;4BAACC,OAAO,eAAErF,OAAA,CAACyC,QAAQ;8BAAAuB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC7CnE,OAAA,CAAChB,KAAK;4BAACoG,IAAI,EAAC,gBAAgB;4BAACC,OAAO,eAAErF,OAAA,CAAC4C,aAAa;8BAAAoB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC3DnE,OAAA,CAAChB,KAAK;4BAACoG,IAAI,EAAC,OAAO;4BAACC,OAAO,eACzBrF,OAAA,CAACF,aAAa;8BAACwF,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;8BAAApB,QAAA,eACrDrE,OAAA,CAAC2D,aAAa;gCAAAK,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACJ;0BAChB;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eACJnE,OAAA,CAAChB,KAAK;4BAACoG,IAAI,EAAC,QAAQ;4BAACC,OAAO,eAAErF,OAAA,CAACsC,KAAK;8BAAA0B,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC,eAC3CnE,OAAA,CAAChB,KAAK;4BAACoG,IAAI,EAAC,YAAY;4BAACC,OAAO,eAAErF,OAAA,CAAC+C,QAAQ;8BAAAiB,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAAE;0BAAE;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAE,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAC5C;sBAGT;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEFnE,OAAA,CAAChB,KAAK;wBACJoG,IAAI,EAAC,SAAS;wBACdC,OAAO,eACLrF,OAAA,CAACV,YAAY;0BAAA+E,QAAA,eACXrE,OAAA,CAACZ,iBAAiB;4BAAAiF,QAAA,eAChBrE,OAAA,CAACjB,MAAM;8BAAAsF,QAAA,gBACLrE,OAAA,CAAChB,KAAK;gCAACoG,IAAI,EAAC,QAAQ;gCAACC,OAAO,eAAErF,OAAA,CAACsC,KAAK;kCAAA0B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAE;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC3CnE,OAAA,CAAChB,KAAK;gCAACoG,IAAI,EAAC,WAAW;gCAACC,OAAO,eAAErF,OAAA,CAACJ,cAAc;kCAACyF,OAAO,eAAErF,OAAA,CAACwD,SAAS;oCAAAQ,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAE;kCAACuB,kBAAkB,EAAE,KAAM;kCAACC,WAAW,EAAE;gCAAK;kCAAA3B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAE;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAC7HnE,OAAA,CAAChB,KAAK;gCAACoG,IAAI,EAAC,aAAa;gCAACC,OAAO,eAAErF,OAAA,CAACJ,cAAc;kCAACyF,OAAO,eAAErF,OAAA,CAACd,UAAU;oCAAA8E,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAE;kCAACuB,kBAAkB,EAAE,KAAM;kCAACC,WAAW,EAAE;gCAAK;kCAAA3B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAE;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC,eAChInE,OAAA,CAAChB,KAAK;gCAACoG,IAAI,EAAC,EAAE;gCAACC,OAAO,eAAErF,OAAA,CAACJ,cAAc;kCAACyF,OAAO,eAAErF,OAAA,CAACuE,aAAa;oCAAAP,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE,CAAE;kCAACuB,kBAAkB,EAAE;gCAAK;kCAAA1B,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OAAE;8BAAE;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE,CAAC;4BAAA;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAC9F;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR;sBAEf;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC,eAEFnE,OAAA,CAAChB,KAAK;wBACJoG,IAAI,EAAC,cAAc;wBACnBC,OAAO,eACLrF,OAAA,CAACV,YAAY;0BAAA+E,QAAA,eACXrE,OAAA,CAACZ,iBAAiB;4BAAAiF,QAAA,eAChBrE,OAAA,CAACjB,MAAM;8BAAAsF,QAAA,eACLrE,OAAA,CAAChB,KAAK;gCAACoG,IAAI,EAAC,EAAE;gCAACC,OAAO,eACpBrF,OAAA,CAACF,aAAa;kCAACwF,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;kCAAApB,QAAA,eACrDrE,OAAA,CAAC0E,kBAAkB;oCAAAV,QAAA,EAAAC,YAAA;oCAAAC,UAAA;oCAAAC,YAAA;kCAAA,OAAE;gCAAC;kCAAAH,QAAA,EAAAC,YAAA;kCAAAC,UAAA;kCAAAC,YAAA;gCAAA,OACT;8BAChB;gCAAAH,QAAA,EAAAC,YAAA;gCAAAC,UAAA;gCAAAC,YAAA;8BAAA,OAAE;4BAAC;8BAAAH,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OACE;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACQ;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACR;sBAEf;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACF,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACI;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACG;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAEE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAEC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACD,CAAC,eACtBnE,OAAA,CAACH,cAAc;cAAAmE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACJ;EAAC,gBAChB,CAAC;AAET;AAACY,GAAA,CAvFQD,GAAG;EAAA,QACa7F,eAAe;AAAA;AAAA2G,IAAA,GAD/Bd,GAAG;AAyFZ,eAAeA,GAAG;AAAC,IAAAxE,EAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,GAAA,EAAAE,GAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAE,IAAA,EAAAC,IAAA,EAAAY,IAAA,EAAAG,IAAA,EAAAI,IAAA,EAAAe,IAAA;AAAAC,YAAA,CAAAvF,EAAA;AAAAuF,YAAA,CAAAtF,GAAA;AAAAsF,YAAA,CAAApF,GAAA;AAAAoF,YAAA,CAAAnF,GAAA;AAAAmF,YAAA,CAAAjF,GAAA;AAAAiF,YAAA,CAAAhF,GAAA;AAAAgF,YAAA,CAAA9E,GAAA;AAAA8E,YAAA,CAAA7E,GAAA;AAAA6E,YAAA,CAAA3E,GAAA;AAAA2E,YAAA,CAAA1E,IAAA;AAAA0E,YAAA,CAAAxE,IAAA;AAAAwE,YAAA,CAAAvE,IAAA;AAAAuE,YAAA,CAAArE,IAAA;AAAAqE,YAAA,CAAApE,IAAA;AAAAoE,YAAA,CAAAlE,IAAA;AAAAkE,YAAA,CAAAjE,IAAA;AAAAiE,YAAA,CAAA/D,IAAA;AAAA+D,YAAA,CAAA9D,IAAA;AAAA8D,YAAA,CAAA5D,IAAA;AAAA4D,YAAA,CAAA3D,IAAA;AAAA2D,YAAA,CAAAzD,IAAA;AAAAyD,YAAA,CAAAxD,IAAA;AAAAwD,YAAA,CAAAtD,IAAA;AAAAsD,YAAA,CAAArD,IAAA;AAAAqD,YAAA,CAAAnD,IAAA;AAAAmD,YAAA,CAAAlD,IAAA;AAAAkD,YAAA,CAAAhD,IAAA;AAAAgD,YAAA,CAAA/C,IAAA;AAAA+C,YAAA,CAAA7C,IAAA;AAAA6C,YAAA,CAAA5C,IAAA;AAAA4C,YAAA,CAAA1C,IAAA;AAAA0C,YAAA,CAAAzC,IAAA;AAAAyC,YAAA,CAAAvC,IAAA;AAAAuC,YAAA,CAAAtC,IAAA;AAAAsC,YAAA,CAAApC,IAAA;AAAAoC,YAAA,CAAAnC,IAAA;AAAAmC,YAAA,CAAAvB,IAAA;AAAAuB,YAAA,CAAApB,IAAA;AAAAoB,YAAA,CAAAhB,IAAA;AAAAgB,YAAA,CAAAD,IAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}