[{"C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\index.tsx": "1", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\App.tsx": "2", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\authContext.tsx": "3", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\playerContext.tsx": "4", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\hostContext.tsx": "5", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\timeListenerContext.tsx": "6", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\soundContext.tsx": "7", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\routes\\ProtectedRoute.tsx": "8", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.tsx": "9", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\FallBack.tsx": "10", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Spectator\\SpectatorJoin.tsx": "11", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.tsx": "12", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\JoinRoom\\JoinRoom.tsx": "13", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Login\\Login.tsx": "14", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.tsx": "15", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.tsx": "16", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.tsx": "17", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.tsx": "18", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.tsx": "19", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Home\\Home.tsx": "20", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.tsx": "21", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Room\\CreateRoom.tsx": "22", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound3.tsx": "23", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRoundTurn.tsx": "24", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound4.tsx": "25", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound2.tsx": "26", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\Dashboard.tsx": "27", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound1.tsx": "28", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\services.ts": "29", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\service.ts": "30", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\service.ts": "31", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\firebaseServices.ts": "32", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\services.ts": "33", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\auth.service.ts": "34", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.tsx": "35", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useAuth.ts": "36", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\uploadAssestServices.ts": "37", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\room.service.ts": "38", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Footer.tsx": "39", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.tsx": "40", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.tsx": "41", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.tsx": "42", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\UploadTest.tsx": "43", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\SetUpMatch.tsx": "44", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\ViewTest.tsx": "45", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRoundTurn.tsx": "46", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.tsx": "47", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRound4.tsx": "48", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\History.tsx": "49", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Host\\Host.tsx": "50", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRoundTurn.tsx": "51", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRound4.tsx": "52", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\http.ts": "53", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\firebase-config.ts": "54", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.tsx": "55", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Header.tsx": "56", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\services.ts": "57", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\testManagement.service.ts": "58", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\utils.ts": "59", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useListener.ts": "60", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\PlayerAnswerInput.tsx": "61", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\GameGrid.tsx": "62", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.tsx": "63", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.tsx": "64", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostManagement.tsx": "65", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostAnswer.tsx": "66", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\utils\\processFile.utils.ts": "67", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostQuestionPreview.tsx": "68", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\RulesModal.tsx": "69", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostGuideModal.tsx": "70", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerColorSelector.tsx": "71", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\SimpleColorPicker.tsx": "72", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\tokenRefresh.service.ts": "73", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useTokenRefresh.ts": "74", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ErrorBoundary.tsx": "75", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\providers\\ReduxProvider.tsx": "76", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\index.ts": "77", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\authSlice.ts": "78", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\uiSlice.ts": "79", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\roomSlice.ts": "80", "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\gameSlice.ts": "81"}, {"size": 531, "mtime": 1742041483045, "results": "82", "hashOfConfig": "83"}, {"size": 7794, "mtime": 1753267496308, "results": "84", "hashOfConfig": "83"}, {"size": 1945, "mtime": 1751515978853, "results": "85", "hashOfConfig": "83"}, {"size": 3318, "mtime": 1751205902719, "results": "86", "hashOfConfig": "83"}, {"size": 12318, "mtime": 1751815416920, "results": "87", "hashOfConfig": "83"}, {"size": 3007, "mtime": 1750132275454, "results": "88", "hashOfConfig": "83"}, {"size": 2210, "mtime": 1748877596389, "results": "89", "hashOfConfig": "83"}, {"size": 5742, "mtime": 1751515978847, "results": "90", "hashOfConfig": "83"}, {"size": 3063, "mtime": 1750646733694, "results": "91", "hashOfConfig": "83"}, {"size": 10853, "mtime": 1751813331809, "results": "92", "hashOfConfig": "83"}, {"size": 5916, "mtime": 1751468518454, "results": "93", "hashOfConfig": "83"}, {"size": 1091, "mtime": 1750606536117, "results": "94", "hashOfConfig": "83"}, {"size": 8831, "mtime": 1751469082283, "results": "95", "hashOfConfig": "83"}, {"size": 5973, "mtime": 1751467878917, "results": "96", "hashOfConfig": "83"}, {"size": 2428, "mtime": 1748535778291, "results": "97", "hashOfConfig": "83"}, {"size": 2985, "mtime": 1748882058132, "results": "98", "hashOfConfig": "83"}, {"size": 2519, "mtime": 1750907852110, "results": "99", "hashOfConfig": "83"}, {"size": 9153, "mtime": 1752762362793, "results": "100", "hashOfConfig": "83"}, {"size": 3560, "mtime": 1750858199098, "results": "101", "hashOfConfig": "83"}, {"size": 9231, "mtime": 1748535442800, "results": "102", "hashOfConfig": "83"}, {"size": 2434, "mtime": 1748537556126, "results": "103", "hashOfConfig": "83"}, {"size": 2950, "mtime": 1749916604844, "results": "104", "hashOfConfig": "83"}, {"size": 286, "mtime": 1744882291961, "results": "105", "hashOfConfig": "83"}, {"size": 500, "mtime": 1750907175563, "results": "106", "hashOfConfig": "83"}, {"size": 1202, "mtime": 1749697405299, "results": "107", "hashOfConfig": "83"}, {"size": 1350, "mtime": 1747936224953, "results": "108", "hashOfConfig": "83"}, {"size": 6142, "mtime": 1751771488939, "results": "109", "hashOfConfig": "83"}, {"size": 391, "mtime": 1748164720734, "results": "110", "hashOfConfig": "83"}, {"size": 16423, "mtime": 1752829867448, "results": "111", "hashOfConfig": "83"}, {"size": 12288, "mtime": 1752803800875, "results": "112", "hashOfConfig": "83"}, {"size": 4013, "mtime": 1752755496277, "results": "113", "hashOfConfig": "83"}, {"size": 19532, "mtime": 1751705284770, "results": "114", "hashOfConfig": "83"}, {"size": 9582, "mtime": 1752720987966, "results": "115", "hashOfConfig": "83"}, {"size": 1461, "mtime": 1751771152469, "results": "116", "hashOfConfig": "83"}, {"size": 2665, "mtime": 1750606460471, "results": "117", "hashOfConfig": "83"}, {"size": 3650, "mtime": 1749640904327, "results": "118", "hashOfConfig": "83"}, {"size": 1791, "mtime": 1751515978848, "results": "119", "hashOfConfig": "83"}, {"size": 1175, "mtime": 1752727399105, "results": "120", "hashOfConfig": "83"}, {"size": 1170, "mtime": 1741426442370, "results": "121", "hashOfConfig": "83"}, {"size": 9006, "mtime": 1751816803836, "results": "122", "hashOfConfig": "83"}, {"size": 35961, "mtime": 1751815841773, "results": "123", "hashOfConfig": "83"}, {"size": 21763, "mtime": 1751815785464, "results": "124", "hashOfConfig": "83"}, {"size": 4939, "mtime": 1750003466586, "results": "125", "hashOfConfig": "83"}, {"size": 27019, "mtime": 1752740248775, "results": "126", "hashOfConfig": "83"}, {"size": 18850, "mtime": 1752738498726, "results": "127", "hashOfConfig": "83"}, {"size": 8496, "mtime": 1750907101790, "results": "128", "hashOfConfig": "83"}, {"size": 575, "mtime": 1748534874974, "results": "129", "hashOfConfig": "83"}, {"size": 5356, "mtime": 1750860093881, "results": "130", "hashOfConfig": "83"}, {"size": 7200, "mtime": 1750743744045, "results": "131", "hashOfConfig": "83"}, {"size": 524, "mtime": 1748606359769, "results": "132", "hashOfConfig": "83"}, {"size": 8786, "mtime": 1751211350591, "results": "133", "hashOfConfig": "83"}, {"size": 19437, "mtime": 1751783887714, "results": "134", "hashOfConfig": "83"}, {"size": 3477, "mtime": 1752737148040, "results": "135", "hashOfConfig": "83"}, {"size": 787, "mtime": 1748599490140, "results": "136", "hashOfConfig": "83"}, {"size": 5432, "mtime": 1751780054124, "results": "137", "hashOfConfig": "83"}, {"size": 4866, "mtime": 1751714260898, "results": "138", "hashOfConfig": "83"}, {"size": 2464, "mtime": 1752829137284, "results": "139", "hashOfConfig": "83"}, {"size": 968, "mtime": 1750004006914, "results": "140", "hashOfConfig": "83"}, {"size": 11090, "mtime": 1749823808632, "results": "141", "hashOfConfig": "83"}, {"size": 7991, "mtime": 1750859938041, "results": "142", "hashOfConfig": "83"}, {"size": 2132, "mtime": 1751714717852, "results": "143", "hashOfConfig": "83"}, {"size": 6286, "mtime": 1749654059721, "results": "144", "hashOfConfig": "83"}, {"size": 14956, "mtime": 1751815334305, "results": "145", "hashOfConfig": "83"}, {"size": 9062, "mtime": 1751779544380, "results": "146", "hashOfConfig": "83"}, {"size": 18061, "mtime": 1752804447146, "results": "147", "hashOfConfig": "83"}, {"size": 20547, "mtime": 1751709091207, "results": "148", "hashOfConfig": "83"}, {"size": 326, "mtime": 1750003544449, "results": "149", "hashOfConfig": "83"}, {"size": 2678, "mtime": 1751210716770, "results": "150", "hashOfConfig": "83"}, {"size": 8734, "mtime": 1751366308588, "results": "151", "hashOfConfig": "83"}, {"size": 8700, "mtime": 1751716766283, "results": "152", "hashOfConfig": "83"}, {"size": 9219, "mtime": 1751446268973, "results": "153", "hashOfConfig": "83"}, {"size": 3672, "mtime": 1751446814342, "results": "154", "hashOfConfig": "83"}, {"size": 5456, "mtime": 1751471046304, "results": "155", "hashOfConfig": "83"}, {"size": 3507, "mtime": 1751470890921, "results": "156", "hashOfConfig": "83"}, {"size": 1444, "mtime": 1751812774732, "results": "157", "hashOfConfig": "83"}, {"size": 371, "mtime": 1753267395618, "results": "158", "hashOfConfig": "83"}, {"size": 1044, "mtime": 1753267066457, "results": "159", "hashOfConfig": "83"}, {"size": 5691, "mtime": 1753267189603, "results": "160", "hashOfConfig": "83"}, {"size": 6667, "mtime": 1753267280970, "results": "161", "hashOfConfig": "83"}, {"size": 8530, "mtime": 1753267252448, "results": "162", "hashOfConfig": "83"}, {"size": 7791, "mtime": 1753267220917, "results": "163", "hashOfConfig": "83"}, {"filePath": "164", "messages": "165", "suppressedMessages": "166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "xa5pjk", {"filePath": "167", "messages": "168", "suppressedMessages": "169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "170", "messages": "171", "suppressedMessages": "172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "173", "messages": "174", "suppressedMessages": "175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "176", "messages": "177", "suppressedMessages": "178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "179", "messages": "180", "suppressedMessages": "181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "182", "messages": "183", "suppressedMessages": "184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "185", "messages": "186", "suppressedMessages": "187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "188", "messages": "189", "suppressedMessages": "190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "191", "messages": "192", "suppressedMessages": "193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "194", "messages": "195", "suppressedMessages": "196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "197", "messages": "198", "suppressedMessages": "199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "200", "messages": "201", "suppressedMessages": "202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "203", "messages": "204", "suppressedMessages": "205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "206", "messages": "207", "suppressedMessages": "208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "209", "messages": "210", "suppressedMessages": "211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "212", "messages": "213", "suppressedMessages": "214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "215", "messages": "216", "suppressedMessages": "217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "218", "messages": "219", "suppressedMessages": "220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "221", "messages": "222", "suppressedMessages": "223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "224", "messages": "225", "suppressedMessages": "226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "227", "messages": "228", "suppressedMessages": "229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "230", "messages": "231", "suppressedMessages": "232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "233", "messages": "234", "suppressedMessages": "235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "236", "messages": "237", "suppressedMessages": "238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "239", "messages": "240", "suppressedMessages": "241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "242", "messages": "243", "suppressedMessages": "244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "245", "messages": "246", "suppressedMessages": "247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "248", "messages": "249", "suppressedMessages": "250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "251", "messages": "252", "suppressedMessages": "253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "254", "messages": "255", "suppressedMessages": "256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "257", "messages": "258", "suppressedMessages": "259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "260", "messages": "261", "suppressedMessages": "262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "263", "messages": "264", "suppressedMessages": "265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "266", "messages": "267", "suppressedMessages": "268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "269", "messages": "270", "suppressedMessages": "271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "272", "messages": "273", "suppressedMessages": "274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "275", "messages": "276", "suppressedMessages": "277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "278", "messages": "279", "suppressedMessages": "280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "281", "messages": "282", "suppressedMessages": "283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "284", "messages": "285", "suppressedMessages": "286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "287", "messages": "288", "suppressedMessages": "289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 29, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "290", "messages": "291", "suppressedMessages": "292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "293", "messages": "294", "suppressedMessages": "295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "296", "messages": "297", "suppressedMessages": "298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "299", "messages": "300", "suppressedMessages": "301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "302", "messages": "303", "suppressedMessages": "304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "305", "messages": "306", "suppressedMessages": "307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "308", "messages": "309", "suppressedMessages": "310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "311", "messages": "312", "suppressedMessages": "313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "314", "messages": "315", "suppressedMessages": "316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "317", "messages": "318", "suppressedMessages": "319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 43, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\App.tsx", ["407", "408", "409", "410", "411", "412"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\authContext.tsx", ["413", "414", "415", "416", "417", "418", "419", "420", "421", "422", "423"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\playerContext.tsx", ["424", "425", "426"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\hostContext.tsx", ["427", "428", "429", "430", "431", "432", "433", "434", "435", "436", "437", "438"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\timeListenerContext.tsx", ["439", "440", "441", "442", "443", "444", "445", "446"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\context\\soundContext.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\routes\\ProtectedRoute.tsx", ["447"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\HostFinalScore.tsx", ["448", "449", "450", "451"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\FallBack.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Spectator\\SpectatorJoin.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\FinalScore\\PlayerFinalScore.tsx", ["452"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\JoinRoom\\JoinRoom.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Login\\Login.tsx", ["453"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\UserRound1.tsx", ["454", "455"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\UserRound2.tsx", ["456", "457", "458", "459", "460", "461", "462", "463", "464"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\RoundTurn\\UserRoundTurn.tsx", ["465", "466"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\InformationForm.tsx", ["467"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round4\\UserRound4.tsx", ["468", "469", "470", "471", "472"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Home\\Home.tsx", ["473"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round3\\UserRound3.tsx", ["474", "475", "476"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Room\\CreateRoom.tsx", ["477", "478", "479", "480"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound3.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRoundTurn.tsx", ["481", "482"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound4.tsx", ["483", "484"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound2.tsx", ["485"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\Dashboard.tsx", ["486"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\HostRound1.tsx", ["487"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\services.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\service.ts", ["488", "489"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Management\\service.ts", ["490", "491"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\firebaseServices.ts", ["492", "493", "494", "495"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\InformationForm\\services.ts", ["496", "497"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\auth.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\FinalScore.tsx", ["498", "499", "500", "501", "502"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useAuth.ts", ["503", "504", "505"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\uploadAssestServices.ts", ["506"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\room.service.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Footer.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round1.tsx", ["507", "508", "509", "510", "511", "512", "513", "514", "515", "516", "517", "518"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round2.tsx", ["519", "520", "521", "522", "523", "524", "525", "526", "527", "528", "529", "530", "531", "532", "533", "534", "535", "536", "537", "538", "539", "540", "541", "542", "543", "544", "545", "546", "547"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Round3.tsx", ["548", "549", "550", "551", "552", "553", "554", "555", "556", "557", "558", "559", "560", "561", "562", "563", "564", "565", "566", "567", "568", "569", "570", "571", "572", "573", "574", "575", "576"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\UploadTest.tsx", ["577", "578"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\SetUpMatch.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\ViewTest.tsx", ["579", "580", "581", "582", "583", "584"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRoundTurn.tsx", ["585", "586", "587", "588", "589", "590", "591", "592", "593", "594"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\User\\User.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Player\\PlayerQuestionBoxRound4.tsx", ["595", "596", "597", "598", "599", "600", "601", "602", "603", "604", "605", "606", "607", "608", "609", "610", "611", "612", "613", "614", "615"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\Host\\Test\\History.tsx", ["616", "617", "618", "619", "620", "621", "622", "623", "624", "625"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Host\\Host.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRoundTurn.tsx", ["626", "627", "628", "629", "630", "631", "632", "633", "634", "635"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\RoundBase\\Host\\HostQuestionBoxRound4.tsx", ["636", "637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654", "655", "656", "657", "658", "659", "660", "661"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\http.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\firebase-config.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerScore.tsx", ["662"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Header.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\services.ts", ["663"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\testManagement.service.ts", ["664"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\pages\\User\\Round2\\utils.ts", ["665", "666", "667", "668", "669", "670", "671", "672", "673"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useListener.ts", ["674", "675", "676", "677", "678", "679", "680", "681", "682", "683"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\PlayerAnswerInput.tsx", ["684"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ui\\GameGrid.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\layouts\\Play.tsx", ["685", "686", "687", "688", "689", "690", "691", "692", "693", "694", "695", "696", "697", "698", "699", "700", "701", "702", "703", "704", "705", "706", "707", "708", "709", "710", "711", "712", "713", "714", "715", "716", "717", "718", "719", "720", "721", "722", "723", "724", "725", "726", "727"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerAnswer.tsx", ["728", "729", "730", "731", "732"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostManagement.tsx", ["733", "734", "735", "736", "737", "738", "739", "740"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostAnswer.tsx", ["741", "742", "743", "744", "745", "746", "747"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\utils\\processFile.utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostQuestionPreview.tsx", ["748", "749", "750", "751"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\RulesModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\HostGuideModal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\PlayerColorSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\SimpleColorPicker.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\services\\tokenRefresh.service.ts", ["752"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\hooks\\useTokenRefresh.ts", ["753"], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\providers\\ReduxProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\roomSlice.ts", [], [], "C:\\Users\\<USER>\\Documents\\New HTM\\htm_fe\\src\\app\\store\\slices\\gameSlice.ts", [], [], {"ruleId": "754", "severity": 1, "message": "755", "line": 2, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 36}, {"ruleId": "754", "severity": 1, "message": "758", "line": 2, "column": 38, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 44}, {"ruleId": "754", "severity": 1, "message": "759", "line": 3, "column": 25, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 36}, {"ruleId": "754", "severity": 1, "message": "760", "line": 5, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "761", "line": 16, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 16, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "762", "line": 92, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 92, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "755", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "763", "line": 3, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "764", "line": 5, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "765", "line": 6, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "766", "line": 8, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 10}, {"ruleId": "754", "severity": 1, "message": "767", "line": 9, "column": 3, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "768", "line": 17, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "769", "line": 17, "column": 25, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 41}, {"ruleId": "754", "severity": 1, "message": "770", "line": 18, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "771", "line": 18, "column": 16, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "772", "line": 21, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "755", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "773", "line": 17, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "774", "line": 17, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "775", "line": 7, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 27}, {"ruleId": "754", "severity": 1, "message": "776", "line": 8, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "777", "line": 8, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 51}, {"ruleId": "754", "severity": 1, "message": "778", "line": 10, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 10, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "779", "line": 15, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 35}, {"ruleId": "754", "severity": 1, "message": "780", "line": 20, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 20, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "781", "line": 21, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "782", "line": 27, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 27, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "783", "line": 45, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 45, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "784", "line": 45, "column": 20, "nodeType": "756", "messageId": "757", "endLine": 45, "endColumn": 31}, {"ruleId": "754", "severity": 1, "message": "785", "line": 56, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 56, "endColumn": 39}, {"ruleId": "786", "severity": 1, "message": "787", "line": 72, "column": 6, "nodeType": "788", "endLine": 72, "endColumn": 8, "suggestions": "789"}, {"ruleId": "754", "severity": 1, "message": "755", "line": 1, "column": 44, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 53}, {"ruleId": "754", "severity": 1, "message": "790", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "791", "line": 2, "column": 22, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "778", "line": 4, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "792", "line": 6, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "781", "line": 30, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 30, "endColumn": 15}, {"ruleId": "754", "severity": 1, "message": "793", "line": 33, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "794", "line": 71, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 71, "endColumn": 23}, {"ruleId": "786", "severity": 1, "message": "795", "line": 130, "column": 8, "nodeType": "788", "endLine": 130, "endColumn": 60, "suggestions": "796"}, {"ruleId": "754", "severity": 1, "message": "797", "line": 1, "column": 38, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 47}, {"ruleId": "754", "severity": 1, "message": "758", "line": 1, "column": 49, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 55}, {"ruleId": "754", "severity": 1, "message": "798", "line": 1, "column": 57, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 68}, {"ruleId": "786", "severity": 1, "message": "799", "line": 54, "column": 6, "nodeType": "788", "endLine": 54, "endColumn": 8, "suggestions": "800"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 27, "column": 8, "nodeType": "788", "endLine": 27, "endColumn": 10, "suggestions": "802"}, {"ruleId": "803", "severity": 1, "message": "804", "line": 100, "column": 17, "nodeType": "805", "endLine": 100, "endColumn": 101}, {"ruleId": "754", "severity": 1, "message": "806", "line": 20, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 20, "endColumn": 21}, {"ruleId": "786", "severity": 1, "message": "807", "line": 66, "column": 8, "nodeType": "788", "endLine": 66, "endColumn": 16, "suggestions": "808"}, {"ruleId": "754", "severity": 1, "message": "809", "line": 4, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "810", "line": 6, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "811", "line": 17, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "812", "line": 17, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "813", "line": 18, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "814", "line": 18, "column": 21, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "815", "line": 21, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 21, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "806", "line": 28, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 28, "endColumn": 19}, {"ruleId": "786", "severity": 1, "message": "807", "line": 73, "column": 6, "nodeType": "788", "endLine": 73, "endColumn": 14, "suggestions": "816"}, {"ruleId": "754", "severity": 1, "message": "806", "line": 20, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 20, "endColumn": 21}, {"ruleId": "786", "severity": 1, "message": "807", "line": 66, "column": 8, "nodeType": "788", "endLine": 66, "endColumn": 16, "suggestions": "817"}, {"ruleId": "754", "severity": 1, "message": "818", "line": 3, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "819", "line": 1, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "820", "line": 8, "column": 7, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "821", "line": 36, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 36, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "806", "line": 38, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 38, "endColumn": 21}, {"ruleId": "786", "severity": 1, "message": "807", "line": 83, "column": 8, "nodeType": "788", "endLine": 83, "endColumn": 16, "suggestions": "822"}, {"ruleId": "786", "severity": 1, "message": "823", "line": 20, "column": 6, "nodeType": "788", "endLine": 20, "endColumn": 8, "suggestions": "824"}, {"ruleId": "754", "severity": 1, "message": "815", "line": 14, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 14, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "806", "line": 22, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 21}, {"ruleId": "786", "severity": 1, "message": "807", "line": 67, "column": 8, "nodeType": "788", "endLine": 67, "endColumn": 16, "suggestions": "825"}, {"ruleId": "754", "severity": 1, "message": "755", "line": 1, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 36}, {"ruleId": "754", "severity": 1, "message": "826", "line": 3, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "827", "line": 4, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "828", "line": 7, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "829", "line": 1, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "830", "line": 4, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "819", "line": 1, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 14}, {"ruleId": "754", "severity": 1, "message": "831", "line": 7, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 12}, {"ruleId": "786", "severity": 1, "message": "799", "line": 32, "column": 7, "nodeType": "788", "endLine": 32, "endColumn": 9, "suggestions": "832"}, {"ruleId": "754", "severity": 1, "message": "833", "line": 15, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 15, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "829", "line": 1, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 14}, {"ruleId": "834", "severity": 1, "message": "835", "line": 60, "column": 25, "nodeType": "836", "messageId": "837", "endLine": 60, "endColumn": 27}, {"ruleId": "834", "severity": 1, "message": "835", "line": 65, "column": 61, "nodeType": "836", "messageId": "837", "endLine": 65, "endColumn": 63}, {"ruleId": "754", "severity": 1, "message": "838", "line": 1, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "839", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "838", "line": 3, "column": 16, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "755", "line": 5, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "840", "line": 19, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 19, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "841", "line": 27, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 27, "endColumn": 18}, {"ruleId": "834", "severity": 1, "message": "835", "line": 89, "column": 25, "nodeType": "836", "messageId": "837", "endLine": 89, "endColumn": 27}, {"ruleId": "834", "severity": 1, "message": "835", "line": 94, "column": 61, "nodeType": "836", "messageId": "837", "endLine": 94, "endColumn": 63}, {"ruleId": "754", "severity": 1, "message": "842", "line": 1, "column": 17, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "755", "line": 1, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 36}, {"ruleId": "754", "severity": 1, "message": "758", "line": 1, "column": 49, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 55}, {"ruleId": "754", "severity": 1, "message": "798", "line": 1, "column": 57, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 68}, {"ruleId": "754", "severity": 1, "message": "843", "line": 4, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "844", "line": 14, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 14, "endColumn": 26}, {"ruleId": "754", "severity": 1, "message": "815", "line": 22, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "845", "line": 103, "column": 15, "nodeType": "756", "messageId": "757", "endLine": 103, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "846", "line": 33, "column": 16, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 41}, {"ruleId": "754", "severity": 1, "message": "831", "line": 2, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "847", "line": 3, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "848", "line": 13, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "849", "line": 32, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 32, "endColumn": 37}, {"ruleId": "754", "severity": 1, "message": "784", "line": 35, "column": 53, "nodeType": "756", "messageId": "757", "endLine": 35, "endColumn": 64}, {"ruleId": "754", "severity": 1, "message": "850", "line": 36, "column": 55, "nodeType": "756", "messageId": "757", "endLine": 36, "endColumn": 67}, {"ruleId": "786", "severity": 1, "message": "851", "line": 40, "column": 8, "nodeType": "788", "endLine": 40, "endColumn": 33, "suggestions": "852"}, {"ruleId": "786", "severity": 1, "message": "853", "line": 71, "column": 8, "nodeType": "788", "endLine": 71, "endColumn": 10, "suggestions": "854"}, {"ruleId": "786", "severity": 1, "message": "855", "line": 96, "column": 8, "nodeType": "788", "endLine": 96, "endColumn": 18, "suggestions": "856"}, {"ruleId": "786", "severity": 1, "message": "857", "line": 119, "column": 8, "nodeType": "788", "endLine": 119, "endColumn": 10, "suggestions": "858"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 136, "column": 8, "nodeType": "788", "endLine": 136, "endColumn": 10, "suggestions": "859"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 153, "column": 8, "nodeType": "788", "endLine": 153, "endColumn": 10, "suggestions": "860"}, {"ruleId": "754", "severity": 1, "message": "831", "line": 1, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "861", "line": 3, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "862", "line": 18, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "863", "line": 54, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 54, "endColumn": 27}, {"ruleId": "754", "severity": 1, "message": "864", "line": 60, "column": 7, "nodeType": "756", "messageId": "757", "endLine": 60, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "780", "line": 72, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 72, "endColumn": 11}, {"ruleId": "754", "severity": 1, "message": "784", "line": 74, "column": 33, "nodeType": "756", "messageId": "757", "endLine": 74, "endColumn": 44}, {"ruleId": "754", "severity": 1, "message": "850", "line": 77, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 77, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "865", "line": 101, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 101, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "866", "line": 101, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 101, "endColumn": 45}, {"ruleId": "754", "severity": 1, "message": "867", "line": 102, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 102, "endColumn": 26}, {"ruleId": "754", "severity": 1, "message": "868", "line": 103, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 103, "endColumn": 31}, {"ruleId": "786", "severity": 1, "message": "855", "line": 122, "column": 6, "nodeType": "788", "endLine": 122, "endColumn": 16, "suggestions": "869"}, {"ruleId": "786", "severity": 1, "message": "853", "line": 142, "column": 6, "nodeType": "788", "endLine": 142, "endColumn": 8, "suggestions": "870"}, {"ruleId": "754", "severity": 1, "message": "871", "line": 147, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 147, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "872", "line": 148, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 148, "endColumn": 22}, {"ruleId": "873", "severity": 1, "message": "874", "line": 154, "column": 34, "nodeType": "875", "messageId": "876", "endLine": 154, "endColumn": 36}, {"ruleId": "754", "severity": 1, "message": "877", "line": 206, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 206, "endColumn": 19}, {"ruleId": "786", "severity": 1, "message": "878", "line": 232, "column": 6, "nodeType": "788", "endLine": 232, "endColumn": 14, "suggestions": "879"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 251, "column": 6, "nodeType": "788", "endLine": 251, "endColumn": 8, "suggestions": "880"}, {"ruleId": "873", "severity": 1, "message": "874", "line": 293, "column": 38, "nodeType": "875", "messageId": "876", "endLine": 293, "endColumn": 40}, {"ruleId": "786", "severity": 1, "message": "881", "line": 326, "column": 6, "nodeType": "788", "endLine": 326, "endColumn": 48, "suggestions": "882"}, {"ruleId": "834", "severity": 1, "message": "835", "line": 376, "column": 19, "nodeType": "836", "messageId": "837", "endLine": 376, "endColumn": 21}, {"ruleId": "834", "severity": 1, "message": "835", "line": 436, "column": 19, "nodeType": "836", "messageId": "837", "endLine": 436, "endColumn": 21}, {"ruleId": "786", "severity": 1, "message": "883", "line": 633, "column": 6, "nodeType": "788", "endLine": 633, "endColumn": 20, "suggestions": "884"}, {"ruleId": "786", "severity": 1, "message": "885", "line": 647, "column": 6, "nodeType": "788", "endLine": 647, "endColumn": 8, "suggestions": "886"}, {"ruleId": "786", "severity": 1, "message": "887", "line": 683, "column": 6, "nodeType": "788", "endLine": 683, "endColumn": 20, "suggestions": "888"}, {"ruleId": "786", "severity": 1, "message": "889", "line": 727, "column": 6, "nodeType": "788", "endLine": 727, "endColumn": 20, "suggestions": "890"}, {"ruleId": "786", "severity": 1, "message": "889", "line": 780, "column": 6, "nodeType": "788", "endLine": 780, "endColumn": 20, "suggestions": "891"}, {"ruleId": "754", "severity": 1, "message": "831", "line": 2, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "847", "line": 3, "column": 20, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "892", "line": 6, "column": 97, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 127}, {"ruleId": "754", "severity": 1, "message": "777", "line": 9, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 9, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "893", "line": 12, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 27}, {"ruleId": "754", "severity": 1, "message": "894", "line": 31, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 30}, {"ruleId": "754", "severity": 1, "message": "895", "line": 36, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 36, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "850", "line": 40, "column": 83, "nodeType": "756", "messageId": "757", "endLine": 40, "endColumn": 95}, {"ruleId": "754", "severity": 1, "message": "896", "line": 41, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 41, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "897", "line": 41, "column": 129, "nodeType": "756", "messageId": "757", "endLine": 41, "endColumn": 148}, {"ruleId": "754", "severity": 1, "message": "898", "line": 42, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 42, "endColumn": 38}, {"ruleId": "754", "severity": 1, "message": "899", "line": 43, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 43, "endColumn": 30}, {"ruleId": "754", "severity": 1, "message": "900", "line": 44, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 28}, {"ruleId": "754", "severity": 1, "message": "901", "line": 44, "column": 30, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 49}, {"ruleId": "754", "severity": 1, "message": "902", "line": 48, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 48, "endColumn": 16}, {"ruleId": "754", "severity": 1, "message": "903", "line": 49, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 49, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "784", "line": 50, "column": 35, "nodeType": "756", "messageId": "757", "endLine": 50, "endColumn": 46}, {"ruleId": "754", "severity": 1, "message": "904", "line": 52, "column": 14, "nodeType": "756", "messageId": "757", "endLine": 52, "endColumn": 28}, {"ruleId": "786", "severity": 1, "message": "801", "line": 118, "column": 8, "nodeType": "788", "endLine": 118, "endColumn": 10, "suggestions": "905"}, {"ruleId": "754", "severity": 1, "message": "906", "line": 126, "column": 19, "nodeType": "756", "messageId": "757", "endLine": 126, "endColumn": 26}, {"ruleId": "786", "severity": 1, "message": "795", "line": 137, "column": 8, "nodeType": "788", "endLine": 137, "endColumn": 10, "suggestions": "907"}, {"ruleId": "786", "severity": 1, "message": "908", "line": 174, "column": 8, "nodeType": "788", "endLine": 174, "endColumn": 24, "suggestions": "909"}, {"ruleId": "786", "severity": 1, "message": "853", "line": 207, "column": 8, "nodeType": "788", "endLine": 207, "endColumn": 10, "suggestions": "910"}, {"ruleId": "786", "severity": 1, "message": "911", "line": 236, "column": 8, "nodeType": "788", "endLine": 236, "endColumn": 18, "suggestions": "912"}, {"ruleId": "786", "severity": 1, "message": "913", "line": 246, "column": 8, "nodeType": "788", "endLine": 246, "endColumn": 10, "suggestions": "914"}, {"ruleId": "786", "severity": 1, "message": "795", "line": 259, "column": 8, "nodeType": "788", "endLine": 259, "endColumn": 10, "suggestions": "915"}, {"ruleId": "786", "severity": 1, "message": "916", "line": 279, "column": 8, "nodeType": "788", "endLine": 279, "endColumn": 10, "suggestions": "917"}, {"ruleId": "754", "severity": 1, "message": "906", "line": 293, "column": 19, "nodeType": "756", "messageId": "757", "endLine": 293, "endColumn": 26}, {"ruleId": "786", "severity": 1, "message": "918", "line": 316, "column": 8, "nodeType": "788", "endLine": 316, "endColumn": 10, "suggestions": "919"}, {"ruleId": "754", "severity": 1, "message": "920", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 28}, {"ruleId": "754", "severity": 1, "message": "921", "line": 18, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "922", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "923", "line": 2, "column": 19, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "924", "line": 2, "column": 52, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 66}, {"ruleId": "754", "severity": 1, "message": "925", "line": 5, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "926", "line": 149, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 149, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "927", "line": 160, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 160, "endColumn": 26}, {"ruleId": "754", "severity": 1, "message": "848", "line": 12, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "849", "line": 31, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 37}, {"ruleId": "754", "severity": 1, "message": "784", "line": 33, "column": 53, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 64}, {"ruleId": "754", "severity": 1, "message": "850", "line": 34, "column": 55, "nodeType": "756", "messageId": "757", "endLine": 34, "endColumn": 67}, {"ruleId": "786", "severity": 1, "message": "851", "line": 38, "column": 8, "nodeType": "788", "endLine": 38, "endColumn": 33, "suggestions": "928"}, {"ruleId": "786", "severity": 1, "message": "853", "line": 68, "column": 8, "nodeType": "788", "endLine": 68, "endColumn": 10, "suggestions": "929"}, {"ruleId": "786", "severity": 1, "message": "855", "line": 93, "column": 8, "nodeType": "788", "endLine": 93, "endColumn": 18, "suggestions": "930"}, {"ruleId": "786", "severity": 1, "message": "857", "line": 116, "column": 8, "nodeType": "788", "endLine": 116, "endColumn": 10, "suggestions": "931"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 133, "column": 8, "nodeType": "788", "endLine": 133, "endColumn": 10, "suggestions": "932"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 150, "column": 8, "nodeType": "788", "endLine": 150, "endColumn": 10, "suggestions": "933"}, {"ruleId": "754", "severity": 1, "message": "790", "line": 4, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "934", "line": 4, "column": 41, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 54}, {"ruleId": "754", "severity": 1, "message": "935", "line": 4, "column": 56, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 73}, {"ruleId": "754", "severity": 1, "message": "936", "line": 4, "column": 75, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 95}, {"ruleId": "754", "severity": 1, "message": "937", "line": 4, "column": 97, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 114}, {"ruleId": "754", "severity": 1, "message": "938", "line": 4, "column": 116, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 131}, {"ruleId": "754", "severity": 1, "message": "810", "line": 4, "column": 133, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 148}, {"ruleId": "754", "severity": 1, "message": "939", "line": 4, "column": 150, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 162}, {"ruleId": "754", "severity": 1, "message": "940", "line": 20, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 20, "endColumn": 43}, {"ruleId": "754", "severity": 1, "message": "784", "line": 28, "column": 35, "nodeType": "756", "messageId": "757", "endLine": 28, "endColumn": 46}, {"ruleId": "754", "severity": 1, "message": "941", "line": 29, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 29, "endColumn": 28}, {"ruleId": "754", "severity": 1, "message": "942", "line": 29, "column": 30, "nodeType": "756", "messageId": "757", "endLine": 29, "endColumn": 49}, {"ruleId": "754", "severity": 1, "message": "943", "line": 35, "column": 18, "nodeType": "756", "messageId": "757", "endLine": 35, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "944", "line": 40, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 40, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "945", "line": 44, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "946", "line": 44, "column": 36, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 59}, {"ruleId": "754", "severity": 1, "message": "947", "line": 44, "column": 61, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 82}, {"ruleId": "754", "severity": 1, "message": "948", "line": 44, "column": 84, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 92}, {"ruleId": "754", "severity": 1, "message": "850", "line": 44, "column": 94, "nodeType": "756", "messageId": "757", "endLine": 44, "endColumn": 106}, {"ruleId": "786", "severity": 1, "message": "911", "line": 83, "column": 8, "nodeType": "788", "endLine": 83, "endColumn": 18, "suggestions": "949"}, {"ruleId": "786", "severity": 1, "message": "853", "line": 102, "column": 8, "nodeType": "788", "endLine": 102, "endColumn": 10, "suggestions": "950"}, {"ruleId": "754", "severity": 1, "message": "758", "line": 1, "column": 38, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 44}, {"ruleId": "754", "severity": 1, "message": "922", "line": 2, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "923", "line": 2, "column": 19, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "951", "line": 2, "column": 36, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 50}, {"ruleId": "754", "severity": 1, "message": "924", "line": 2, "column": 52, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 66}, {"ruleId": "754", "severity": 1, "message": "952", "line": 3, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "838", "line": 4, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "925", "line": 5, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "953", "line": 6, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "954", "line": 27, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 27, "endColumn": 30}, {"ruleId": "754", "severity": 1, "message": "848", "line": 12, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 13}, {"ruleId": "754", "severity": 1, "message": "849", "line": 31, "column": 24, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 37}, {"ruleId": "754", "severity": 1, "message": "784", "line": 33, "column": 53, "nodeType": "756", "messageId": "757", "endLine": 33, "endColumn": 64}, {"ruleId": "754", "severity": 1, "message": "850", "line": 34, "column": 55, "nodeType": "756", "messageId": "757", "endLine": 34, "endColumn": 67}, {"ruleId": "786", "severity": 1, "message": "851", "line": 38, "column": 8, "nodeType": "788", "endLine": 38, "endColumn": 33, "suggestions": "955"}, {"ruleId": "786", "severity": 1, "message": "853", "line": 68, "column": 8, "nodeType": "788", "endLine": 68, "endColumn": 10, "suggestions": "956"}, {"ruleId": "786", "severity": 1, "message": "855", "line": 93, "column": 8, "nodeType": "788", "endLine": 93, "endColumn": 18, "suggestions": "957"}, {"ruleId": "786", "severity": 1, "message": "857", "line": 116, "column": 8, "nodeType": "788", "endLine": 116, "endColumn": 10, "suggestions": "958"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 133, "column": 8, "nodeType": "788", "endLine": 133, "endColumn": 10, "suggestions": "959"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 150, "column": 8, "nodeType": "788", "endLine": 150, "endColumn": 10, "suggestions": "960"}, {"ruleId": "754", "severity": 1, "message": "831", "line": 2, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 2, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "847", "line": 3, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 3, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "790", "line": 8, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "791", "line": 8, "column": 22, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 39}, {"ruleId": "754", "severity": 1, "message": "934", "line": 8, "column": 41, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 54}, {"ruleId": "754", "severity": 1, "message": "935", "line": 8, "column": 56, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 73}, {"ruleId": "754", "severity": 1, "message": "936", "line": 8, "column": 75, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 95}, {"ruleId": "754", "severity": 1, "message": "937", "line": 8, "column": 97, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 114}, {"ruleId": "754", "severity": 1, "message": "938", "line": 8, "column": 116, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 131}, {"ruleId": "754", "severity": 1, "message": "810", "line": 8, "column": 133, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 148}, {"ruleId": "754", "severity": 1, "message": "939", "line": 8, "column": 150, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 162}, {"ruleId": "754", "severity": 1, "message": "961", "line": 20, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 20, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "820", "line": 31, "column": 7, "nodeType": "756", "messageId": "757", "endLine": 31, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "962", "line": 40, "column": 7, "nodeType": "756", "messageId": "757", "endLine": 40, "endColumn": 23}, {"ruleId": "754", "severity": 1, "message": "784", "line": 230, "column": 35, "nodeType": "756", "messageId": "757", "endLine": 230, "endColumn": 46}, {"ruleId": "754", "severity": 1, "message": "941", "line": 231, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 231, "endColumn": 28}, {"ruleId": "754", "severity": 1, "message": "945", "line": 246, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 246, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "946", "line": 246, "column": 36, "nodeType": "756", "messageId": "757", "endLine": 246, "endColumn": 59}, {"ruleId": "754", "severity": 1, "message": "947", "line": 246, "column": 61, "nodeType": "756", "messageId": "757", "endLine": 246, "endColumn": 82}, {"ruleId": "754", "severity": 1, "message": "850", "line": 246, "column": 94, "nodeType": "756", "messageId": "757", "endLine": 246, "endColumn": 106}, {"ruleId": "786", "severity": 1, "message": "963", "line": 274, "column": 8, "nodeType": "788", "endLine": 274, "endColumn": 10, "suggestions": "964"}, {"ruleId": "786", "severity": 1, "message": "911", "line": 294, "column": 8, "nodeType": "788", "endLine": 294, "endColumn": 18, "suggestions": "965"}, {"ruleId": "834", "severity": 1, "message": "835", "line": 347, "column": 39, "nodeType": "836", "messageId": "837", "endLine": 347, "endColumn": 41}, {"ruleId": "834", "severity": 1, "message": "835", "line": 351, "column": 39, "nodeType": "836", "messageId": "837", "endLine": 351, "endColumn": 41}, {"ruleId": "834", "severity": 1, "message": "835", "line": 355, "column": 39, "nodeType": "836", "messageId": "837", "endLine": 355, "endColumn": 41}, {"ruleId": "754", "severity": 1, "message": "966", "line": 409, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 409, "endColumn": 30}, {"ruleId": "754", "severity": 1, "message": "967", "line": 13, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "839", "line": 1, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "968", "line": 25, "column": 15, "nodeType": "756", "messageId": "757", "endLine": 25, "endColumn": 27}, {"ruleId": "754", "severity": 1, "message": "838", "line": 10, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 10, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "969", "line": 69, "column": 12, "nodeType": "756", "messageId": "757", "endLine": 69, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "970", "line": 131, "column": 45, "nodeType": "756", "messageId": "757", "endLine": 131, "endColumn": 53}, {"ruleId": "834", "severity": 1, "message": "835", "line": 150, "column": 17, "nodeType": "836", "messageId": "837", "endLine": 150, "endColumn": 19}, {"ruleId": "834", "severity": 1, "message": "835", "line": 155, "column": 21, "nodeType": "836", "messageId": "837", "endLine": 155, "endColumn": 23}, {"ruleId": "971", "severity": 1, "message": "972", "line": 259, "column": 11, "nodeType": "756", "messageId": "973", "endLine": 259, "endColumn": 17}, {"ruleId": "971", "severity": 1, "message": "974", "line": 260, "column": 9, "nodeType": "756", "messageId": "973", "endLine": 260, "endColumn": 15}, {"ruleId": "834", "severity": 1, "message": "835", "line": 272, "column": 34, "nodeType": "836", "messageId": "837", "endLine": 272, "endColumn": 36}, {"ruleId": "834", "severity": 1, "message": "975", "line": 273, "column": 23, "nodeType": "836", "messageId": "837", "endLine": 273, "endColumn": 25}, {"ruleId": "786", "severity": 1, "message": "976", "line": 59, "column": 8, "nodeType": "788", "endLine": 59, "endColumn": 10, "suggestions": "977"}, {"ruleId": "754", "severity": 1, "message": "877", "line": 62, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 62, "endColumn": 23}, {"ruleId": "786", "severity": 1, "message": "978", "line": 88, "column": 8, "nodeType": "788", "endLine": 88, "endColumn": 16, "suggestions": "979"}, {"ruleId": "754", "severity": 1, "message": "877", "line": 92, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 92, "endColumn": 23}, {"ruleId": "786", "severity": 1, "message": "980", "line": 118, "column": 8, "nodeType": "788", "endLine": 118, "endColumn": 16, "suggestions": "981"}, {"ruleId": "786", "severity": 1, "message": "801", "line": 135, "column": 8, "nodeType": "788", "endLine": 135, "endColumn": 10, "suggestions": "982"}, {"ruleId": "786", "severity": 1, "message": "983", "line": 157, "column": 8, "nodeType": "788", "endLine": 157, "endColumn": 10, "suggestions": "984"}, {"ruleId": "786", "severity": 1, "message": "985", "line": 170, "column": 8, "nodeType": "788", "endLine": 170, "endColumn": 10, "suggestions": "986"}, {"ruleId": "786", "severity": 1, "message": "987", "line": 210, "column": 8, "nodeType": "788", "endLine": 210, "endColumn": 10, "suggestions": "988"}, {"ruleId": "786", "severity": 1, "message": "989", "line": 238, "column": 8, "nodeType": "788", "endLine": 238, "endColumn": 10, "suggestions": "990"}, {"ruleId": "786", "severity": 1, "message": "991", "line": 40, "column": 8, "nodeType": "788", "endLine": 40, "endColumn": 18, "suggestions": "992"}, {"ruleId": "754", "severity": 1, "message": "798", "line": 1, "column": 57, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 68}, {"ruleId": "754", "severity": 1, "message": "993", "line": 4, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 16}, {"ruleId": "754", "severity": 1, "message": "994", "line": 4, "column": 18, "nodeType": "756", "messageId": "757", "endLine": 4, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "995", "line": 5, "column": 22, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 37}, {"ruleId": "754", "severity": 1, "message": "996", "line": 5, "column": 39, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 52}, {"ruleId": "754", "severity": 1, "message": "997", "line": 5, "column": 93, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 107}, {"ruleId": "754", "severity": 1, "message": "938", "line": 5, "column": 109, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 124}, {"ruleId": "754", "severity": 1, "message": "791", "line": 5, "column": 126, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 143}, {"ruleId": "754", "severity": 1, "message": "998", "line": 5, "column": 145, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 170}, {"ruleId": "754", "severity": 1, "message": "999", "line": 5, "column": 191, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 209}, {"ruleId": "754", "severity": 1, "message": "1000", "line": 7, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "1001", "line": 8, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "1002", "line": 10, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 10, "endColumn": 22}, {"ruleId": "754", "severity": 1, "message": "1003", "line": 11, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 11, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "1004", "line": 13, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 13, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "777", "line": 14, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 14, "endColumn": 34}, {"ruleId": "754", "severity": 1, "message": "1005", "line": 17, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 12}, {"ruleId": "754", "severity": 1, "message": "1006", "line": 29, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 29, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "1007", "line": 57, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 57, "endColumn": 26}, {"ruleId": "754", "severity": 1, "message": "1008", "line": 63, "column": 20, "nodeType": "756", "messageId": "757", "endLine": 63, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "1009", "line": 66, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "1010", "line": 66, "column": 22, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 32}, {"ruleId": "754", "severity": 1, "message": "1011", "line": 66, "column": 34, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 43}, {"ruleId": "754", "severity": 1, "message": "1012", "line": 66, "column": 45, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 57}, {"ruleId": "754", "severity": 1, "message": "1013", "line": 66, "column": 59, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 68}, {"ruleId": "754", "severity": 1, "message": "1014", "line": 66, "column": 100, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 108}, {"ruleId": "754", "severity": 1, "message": "1015", "line": 66, "column": 110, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 128}, {"ruleId": "754", "severity": 1, "message": "1016", "line": 66, "column": 130, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 143}, {"ruleId": "754", "severity": 1, "message": "1017", "line": 66, "column": 145, "nodeType": "756", "messageId": "757", "endLine": 66, "endColumn": 161}, {"ruleId": "754", "severity": 1, "message": "773", "line": 67, "column": 13, "nodeType": "756", "messageId": "757", "endLine": 67, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "785", "line": 67, "column": 58, "nodeType": "756", "messageId": "757", "endLine": 67, "endColumn": 73}, {"ruleId": "754", "severity": 1, "message": "815", "line": 68, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 68, "endColumn": 20}, {"ruleId": "754", "severity": 1, "message": "1018", "line": 69, "column": 23, "nodeType": "756", "messageId": "757", "endLine": 69, "endColumn": 33}, {"ruleId": "754", "severity": 1, "message": "794", "line": 78, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 78, "endColumn": 25}, {"ruleId": "754", "severity": 1, "message": "1019", "line": 79, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 79, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "1020", "line": 115, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 115, "endColumn": 28}, {"ruleId": "786", "severity": 1, "message": "795", "line": 158, "column": 8, "nodeType": "788", "endLine": 158, "endColumn": 10, "suggestions": "1021"}, {"ruleId": "786", "severity": 1, "message": "1022", "line": 177, "column": 8, "nodeType": "788", "endLine": 177, "endColumn": 16, "suggestions": "1023"}, {"ruleId": "754", "severity": 1, "message": "1024", "line": 181, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 181, "endColumn": 17}, {"ruleId": "754", "severity": 1, "message": "1025", "line": 192, "column": 23, "nodeType": "756", "messageId": "757", "endLine": 192, "endColumn": 35}, {"ruleId": "834", "severity": 1, "message": "975", "line": 195, "column": 28, "nodeType": "836", "messageId": "837", "endLine": 195, "endColumn": 30}, {"ruleId": "834", "severity": 1, "message": "835", "line": 209, "column": 49, "nodeType": "836", "messageId": "837", "endLine": 209, "endColumn": 51}, {"ruleId": "786", "severity": 1, "message": "1026", "line": 236, "column": 8, "nodeType": "788", "endLine": 236, "endColumn": 10, "suggestions": "1027"}, {"ruleId": "754", "severity": 1, "message": "1028", "line": 17, "column": 68, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 86}, {"ruleId": "754", "severity": 1, "message": "1029", "line": 17, "column": 88, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 97}, {"ruleId": "754", "severity": 1, "message": "1030", "line": 17, "column": 99, "nodeType": "756", "messageId": "757", "endLine": 17, "endColumn": 111}, {"ruleId": "786", "severity": 1, "message": "1031", "line": 61, "column": 8, "nodeType": "788", "endLine": 61, "endColumn": 16, "suggestions": "1032"}, {"ruleId": "786", "severity": 1, "message": "878", "line": 87, "column": 8, "nodeType": "788", "endLine": 87, "endColumn": 23, "suggestions": "1033"}, {"ruleId": "754", "severity": 1, "message": "1034", "line": 5, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 5, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "1035", "line": 8, "column": 10, "nodeType": "756", "messageId": "757", "endLine": 8, "endColumn": 21}, {"ruleId": "754", "severity": 1, "message": "1036", "line": 12, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 12, "endColumn": 18}, {"ruleId": "754", "severity": 1, "message": "1037", "line": 22, "column": 5, "nodeType": "756", "messageId": "757", "endLine": 22, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "896", "line": 41, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 41, "endColumn": 29}, {"ruleId": "754", "severity": 1, "message": "1038", "line": 43, "column": 9, "nodeType": "756", "messageId": "757", "endLine": 43, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "1020", "line": 64, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 64, "endColumn": 28}, {"ruleId": "786", "severity": 1, "message": "1039", "line": 118, "column": 8, "nodeType": "788", "endLine": 118, "endColumn": 22, "suggestions": "1040"}, {"ruleId": "754", "severity": 1, "message": "1041", "line": 18, "column": 27, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 40}, {"ruleId": "754", "severity": 1, "message": "1016", "line": 18, "column": 76, "nodeType": "756", "messageId": "757", "endLine": 18, "endColumn": 89}, {"ruleId": "754", "severity": 1, "message": "1042", "line": 19, "column": 63, "nodeType": "756", "messageId": "757", "endLine": 19, "endColumn": 81}, {"ruleId": "786", "severity": 1, "message": "795", "line": 56, "column": 8, "nodeType": "788", "endLine": 56, "endColumn": 15, "suggestions": "1043"}, {"ruleId": "786", "severity": 1, "message": "1044", "line": 64, "column": 8, "nodeType": "788", "endLine": 64, "endColumn": 15, "suggestions": "1045"}, {"ruleId": "786", "severity": 1, "message": "1044", "line": 75, "column": 8, "nodeType": "788", "endLine": 75, "endColumn": 23, "suggestions": "1046"}, {"ruleId": "786", "severity": 1, "message": "1031", "line": 89, "column": 8, "nodeType": "788", "endLine": 89, "endColumn": 16, "suggestions": "1047"}, {"ruleId": "754", "severity": 1, "message": "1048", "line": 6, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 24}, {"ruleId": "754", "severity": 1, "message": "1049", "line": 6, "column": 64, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 81}, {"ruleId": "754", "severity": 1, "message": "896", "line": 6, "column": 83, "nodeType": "756", "messageId": "757", "endLine": 6, "endColumn": 103}, {"ruleId": "754", "severity": 1, "message": "1050", "line": 7, "column": 11, "nodeType": "756", "messageId": "757", "endLine": 7, "endColumn": 26}, {"ruleId": "754", "severity": 1, "message": "1051", "line": 1, "column": 8, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 19}, {"ruleId": "754", "severity": 1, "message": "842", "line": 1, "column": 21, "nodeType": "756", "messageId": "757", "endLine": 1, "endColumn": 29}, "@typescript-eslint/no-unused-vars", "'useEffect' is defined but never used.", "Identifier", "unusedVar", "'useRef' is defined but never used.", "'useLocation' is defined but never used.", "'LoadingSpinner' is defined but never used.", "'withRoleCheck' is defined but never used.", "'roundName' is assigned a value but never used.", "'useAuth' is defined but never used.", "'getAuth' is defined but never used.", "'signInWithEmailAndPassword' is defined but never used.", "'signOut' is defined but never used.", "'onAuthStateChanged' is defined but never used.", "'axiosInstance' is assigned a value but never used.", "'setAxiosInstance' is assigned a value but never used.", "'user' is assigned a value but never used.", "'setUser' is assigned a value but never used.", "'saveToken' is assigned a value but never used.", "'playerScores' is assigned a value but never used.", "'setPlayerScores' is assigned a value but never used.", "'sendGridToPlayers' is defined but never used.", "'setCurrentChunk' is defined but never used.", "'setCurrentPacketQuestion' is defined but never used.", "'round' is defined but never used.", "'setSelectedPacketToPlayer' is defined but never used.", "'testId' is assigned a value but never used.", "'sounds' is assigned a value but never used.", "'navigate' is assigned a value but never used.", "'timeLeft' is assigned a value but never used.", "'setTimeLeft' is assigned a value but never used.", "'setAnimationKey' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'hostRoomId'. Either include it or remove the dependency array.", "ArrayExpression", ["1052"], "'deletePath' is defined but never used.", "'listenToTimeStart' is defined but never used.", "'useHost' is defined but never used.", "'roundRef' is assigned a value but never used.", "'isInitialMount' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'roomId'. Either include it or remove the dependency array.", ["1053"], "'ReactNode' is defined but never used.", "'useCallback' is defined but never used.", "React Hook useEffect has missing dependencies: 'roomId' and 'testName'. Either include them or remove the dependency array.", ["1054"], "React Hook useEffect has missing dependencies: 'roomId' and 'sounds'. Either include them or remove the dependency array.", ["1055"], "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'isAllowed' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'isSpectator', 'navigate', 'round', and 'setInitialGrid'. Either include them or remove the dependency array.", ["1056"], "'ReactPlaceholder' is defined but never used.", "'listenToBuzzing' is defined but never used.", "'buzzedPlayer' is assigned a value but never used.", "'setBuzzedPlayer' is assigned a value but never used.", "'showModal' is assigned a value but never used.", "'setShowModal' is assigned a value but never used.", "'isMounted' is assigned a value but never used.", ["1057"], ["1058"], "'joinRoom' is defined but never used.", "'Round4' is defined but never used.", "'exampleGrid' is assigned a value but never used.", "'loading' is assigned a value but never used.", ["1059"], "React Hook useEffect has a missing dependency: 'images.length'. Either include it or remove the dependency array.", ["1060"], ["1061"], "'banner' is defined but never used.", "'card' is defined but never used.", "'Header' is defined but never used.", "'Round1' is defined but never used.", "'QuestionBoxRound1' is defined but never used.", "'Play' is defined but never used.", ["1062"], "'getToken' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "'Question' is defined but never used.", "'getAxiosAuthContext' is defined but never used.", "'Players' is defined but never used.", "'lastStartTime' is assigned a value but never used.", "'useState' is defined but never used.", "'updateHistory' is defined but never used.", "'authenticateUser' is defined but never used.", "'errorCode' is assigned a value but never used.", "'notifyBackendFileUploaded' is defined but never used.", "'RoundBase' is defined but never used.", "'set' is defined but never used.", "'setIsExpanded' is assigned a value but never used.", "'animationKey' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'playerAnswerRef'. Either include it or remove the dependency array. Mutable values like 'playerAnswerRef.current' aren't valid dependencies because mutating them doesn't re-render the component.", ["1063"], "React Hook useEffect has missing dependencies: 'roomId' and 'startTimer'. Either include them or remove the dependency array.", ["1064"], "React Hook useEffect has missing dependencies: 'currentPlayerAvatar', 'currentPlayerName', 'isHost', 'isSpectator', 'playerAnswerRef', 'playerAnswerTime', 'position', 'roomId', and 'setAnimationKey'. Either include them or remove the dependency array.", ["1065"], "React Hook useEffect has missing dependencies: 'currentAnswer', 'isHost', 'roomId', and 'setAnswerList'. Either include them or remove the dependency array.", ["1066"], ["1067"], ["1068"], "'renderGrid' is defined but never used.", "'HintWord' is defined but never used.", "'QuestionBoxProps' is defined but never used.", "'mainKeyword' is assigned a value but never used.", "'hintWordsLength' is assigned a value but never used.", "'setHintWordsLength' is assigned a value but never used.", "'markedCharacters' is assigned a value but never used.", "'highlightedCharacters' is assigned a value but never used.", ["1069"], ["1070"], "'topBound' is assigned a value but never used.", "'bottomBound' is assigned a value but never used.", "array-callback-return", "Array.prototype.map() expects a return value from arrow function.", "ArrowFunctionExpression", "expectedInside", "'hasMounted' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'sounds'. Either include it or remove the dependency array.", ["1071"], ["1072"], "React Hook useEffect has missing dependencies: 'isHost' and 'setInitialGrid'. Either include them or remove the dependency array.", ["1073"], "React Hook useEffect has a missing dependency: 'revealCellsForPlayer'. Either include it or remove the dependency array.", ["1074"], "React Hook useEffect has missing dependencies: 'roomId' and 'setAnswerList'. Either include them or remove the dependency array.", ["1075"], "React Hook useEffect has missing dependencies: 'revealCellsForPlayer' and 'setAnswerList'. Either include them or remove the dependency array.", ["1076"], "React Hook useEffect has missing dependencies: 'revealCellsForPlayer' and 'sounds'. Either include them or remove the dependency array.", ["1077"], ["1078"], "'listenToCurrentQuestionsNumber' is defined but never used.", "'sendCorrectAnswer' is defined but never used.", "'MAX_PACKET_QUESTION' is assigned a value but never used.", "'hiddenTopics' is assigned a value but never used.", "'currentQuestionIndex' is assigned a value but never used.", "'inGameQuestionIndex' is assigned a value but never used.", "'playerCurrentQuestionIndex' is assigned a value but never used.", "'tempQuestionListRef' is assigned a value but never used.", "'tempQuestionList' is assigned a value but never used.", "'setTempQuestionList' is assigned a value but never used.", "'round' is assigned a value but never used.", "'isFirstMounted' is assigned a value but never used.", "'decodeQuestion' is defined but never used.", ["1079"], "'timeOut' is assigned a value but never used.", ["1080"], "React Hook useEffect has missing dependencies: 'setCurrentQuestion' and 'setSelectedTopic'. Either include them or remove the dependency array.", ["1081"], ["1082"], "React Hook useEffect has a missing dependency: 'setAnimationKey'. Either include it or remove the dependency array.", ["1083"], "React Hook useEffect has missing dependencies: 'isHost', 'roomId', and 'testName'. Either include them or remove the dependency array.", ["1084"], ["1085"], "React Hook useEffect has missing dependencies: 'isHost', 'roomId', and 'setSelectedTopic'. Either include them or remove the dependency array.", ["1086"], "React Hook useEffect has missing dependencies: 'currentAnswer', 'isHost', 'roomId', 'setAnswerList', and 'setCurrentQuestion'. Either include them or remove the dependency array.", ["1087"], "'uploadTestToServer' is defined but never used.", "'response' is assigned a value but never used.", "'getTest' is defined but never used.", "'getTestByUserId' is defined but never used.", "'addNewQuestion' is defined but never used.", "'useQuery' is defined but never used.", "'handleEditClick' is assigned a value but never used.", "'handleAddQuestion' is assigned a value but never used.", ["1088"], ["1089"], ["1090"], ["1091"], ["1092"], ["1093"], "'listenToSound' is defined but never used.", "'listenToQuestions' is defined but never used.", "'listenToSelectedCell' is defined but never used.", "'listenToCellColor' is defined but never used.", "'listenToAnswers' is defined but never used.", "'listenToStar' is defined but never used.", "'colorMap' is assigned a value but never used.", "'selectedQuestion' is assigned a value but never used.", "'setSelectedQuestion' is assigned a value but never used.", "'setMenu' is assigned a value but never used.", "'selectedCell' is assigned a value but never used.", "'setEasyQuestionNumber' is assigned a value but never used.", "'setMediumQuestionNumber' is assigned a value but never used.", "'setHardQuestionNumber' is assigned a value but never used.", "'setLevel' is assigned a value but never used.", ["1094"], ["1095"], "'updateQuestion' is defined but never used.", "'uploadFile' is defined but never used.", "'testManageMentService' is defined but never used.", "'RoomScoreTableProps' is defined but never used.", ["1096"], ["1097"], ["1098"], ["1099"], ["1100"], ["1101"], "'GameGridProps' is defined but never used.", "'exampleQuestions' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'initialGrid' and 'setInitialGrid'. Either include them or remove the dependency array. If 'setGrid' needs the current value of 'initialGrid', you can also switch to useReducer instead of useState and read 'initialGrid' in the reducer.", ["1102"], ["1103"], "'lastBuzzedPlayerRef' is assigned a value but never used.", "'prevOrder' is assigned a value but never used.", "'uploadedFile' is assigned a value but never used.", "'CleanVars' is defined but never used.", "'curMatch' is defined but never used.", "@typescript-eslint/no-redeclare", "'xIndex' is already defined.", "redeclared", "'yIndex' is already defined.", "Expected '!==' and instead saw '!='.", "React Hook useEffect has missing dependencies: 'roomId' and 'startTimer'. Either include them or remove the dependency array. If 'startTimer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1104"], "React Hook useEffect has missing dependencies: 'setBuzzedPlayer', 'setShowModal', and 'sounds'. Either include them or remove the dependency array. If 'setBuzzedPlayer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1105"], "React Hook useEffect has missing dependencies: 'setShowModal', 'setStaredPlayer', and 'sounds'. Either include them or remove the dependency array. If 'setStaredPlayer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1106"], ["1107"], "React Hook useEffect has missing dependencies: 'roomId', 'setCorrectAnswer', and 'sounds'. Either include them or remove the dependency array. If 'setCorrectAnswer' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1108"], "React Hook useEffect has missing dependencies: 'roomId', 'setCorrectAnswer', and 'setCurrentQuestion'. Either include them or remove the dependency array. If 'setCurrentQuestion' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1109"], "React Hook useEffect has missing dependencies: 'roomId', 'setGridColors', and 'setSelectedCell'. Either include them or remove the dependency array. If 'setGridColors' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1110"], "React Hook useEffect has missing dependencies: 'colorMap', 'roomId', 'setGrid', and 'setGridColors'. Either include them or remove the dependency array. If 'setGridColors' changes too often, find the parent component that defines it and wrap that definition in useCallback.", ["1111"], "React Hook useEffect has a missing dependency: 'playerAnswerRef'. Either include it or remove the dependency array.", ["1112"], "'Answer' is defined but never used.", "'User' is defined but never used.", "'addPlayerToRoom' is defined but never used.", "'listenToRules' is defined but never used.", "'listenToScores' is defined but never used.", "'listenToBroadcastedAnswer' is defined but never used.", "'listenToRoundStart' is defined but never used.", "'submitAnswer' is defined but never used.", "'getNextQuestion' is defined but never used.", "'HostManagement' is defined but never used.", "'PlayerScore' is defined but never used.", "'HostScore' is defined but never used.", "'EyeIcon' is defined but never used.", "'Player' is defined but never used.", "'playerAnswerRef' is assigned a value but never used.", "'setUserId' is assigned a value but never used.", "'players' is assigned a value but never used.", "'setPlayers' is assigned a value but never used.", "'setRoomId' is assigned a value but never used.", "'playersArray' is assigned a value but never used.", "'roomRules' is assigned a value but never used.", "'position' is assigned a value but never used.", "'setCurrentQuestion' is assigned a value but never used.", "'selectedTopic' is assigned a value but never used.", "'setSelectedTopic' is assigned a value but never used.", "'startTimer' is assigned a value but never used.", "'styles' is assigned a value but never used.", "'handleRoundChange' is assigned a value but never used.", ["1113"], "React Hook useEffect has a missing dependency: 'setRoomRules'. Either include it or remove the dependency array.", ["1114"], "'isFull' is assigned a value but never used.", "'scoreInitKey' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roomId', 'setPlayerArray', 'setPlayerScores', and 'setScoreList'. Either include them or remove the dependency array.", ["1115"], "'triggerPlayerFlash' is assigned a value but never used.", "'scoreList' is assigned a value but never used.", "'setScoreList' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'setAnswerList'. Either include it or remove the dependency array.", ["1116"], ["1117"], "'openBuzz' is defined but never used.", "'updateScore' is defined but never used.", "'BellAlertIcon' is defined but never used.", "'PaintBrushIcon' is defined but never used.", "'hostInitialGrid' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'roomId' and 'setInGameQuestionIndex'. Either include them or remove the dependency array.", ["1118"], "'playerFlashes' is assigned a value but never used.", "'handleNextQuestion' is assigned a value but never used.", ["1119"], "React Hook useEffect has a missing dependency: 'setPlayerScores'. Either include it or remove the dependency array.", ["1120"], ["1121"], ["1122"], "'currentAnswer' is assigned a value but never used.", "'showCurrentAnswer' is assigned a value but never used.", "'currentQuestion' is assigned a value but never used.", "'authService' is defined but never used.", {"desc": "1123", "fix": "1124"}, {"desc": "1125", "fix": "1126"}, {"desc": "1127", "fix": "1128"}, {"desc": "1129", "fix": "1130"}, {"desc": "1131", "fix": "1132"}, {"desc": "1131", "fix": "1133"}, {"desc": "1131", "fix": "1134"}, {"desc": "1131", "fix": "1135"}, {"desc": "1136", "fix": "1137"}, {"desc": "1131", "fix": "1138"}, {"desc": "1127", "fix": "1139"}, {"desc": "1140", "fix": "1141"}, {"desc": "1142", "fix": "1143"}, {"desc": "1144", "fix": "1145"}, {"desc": "1146", "fix": "1147"}, {"desc": "1129", "fix": "1148"}, {"desc": "1129", "fix": "1149"}, {"desc": "1144", "fix": "1150"}, {"desc": "1142", "fix": "1151"}, {"desc": "1129", "fix": "1152"}, {"desc": "1129", "fix": "1153"}, {"desc": "1154", "fix": "1155"}, {"desc": "1156", "fix": "1157"}, {"desc": "1158", "fix": "1159"}, {"desc": "1160", "fix": "1161"}, {"desc": "1162", "fix": "1163"}, {"desc": "1162", "fix": "1164"}, {"desc": "1129", "fix": "1165"}, {"desc": "1166", "fix": "1167"}, {"desc": "1168", "fix": "1169"}, {"desc": "1142", "fix": "1170"}, {"desc": "1171", "fix": "1172"}, {"desc": "1173", "fix": "1174"}, {"desc": "1166", "fix": "1175"}, {"desc": "1176", "fix": "1177"}, {"desc": "1178", "fix": "1179"}, {"desc": "1140", "fix": "1180"}, {"desc": "1142", "fix": "1181"}, {"desc": "1144", "fix": "1182"}, {"desc": "1146", "fix": "1183"}, {"desc": "1129", "fix": "1184"}, {"desc": "1129", "fix": "1185"}, {"desc": "1171", "fix": "1186"}, {"desc": "1142", "fix": "1187"}, {"desc": "1140", "fix": "1188"}, {"desc": "1142", "fix": "1189"}, {"desc": "1144", "fix": "1190"}, {"desc": "1146", "fix": "1191"}, {"desc": "1129", "fix": "1192"}, {"desc": "1129", "fix": "1193"}, {"desc": "1194", "fix": "1195"}, {"desc": "1171", "fix": "1196"}, {"desc": "1142", "fix": "1197"}, {"desc": "1198", "fix": "1199"}, {"desc": "1200", "fix": "1201"}, {"desc": "1129", "fix": "1202"}, {"desc": "1203", "fix": "1204"}, {"desc": "1205", "fix": "1206"}, {"desc": "1207", "fix": "1208"}, {"desc": "1209", "fix": "1210"}, {"desc": "1211", "fix": "1212"}, {"desc": "1166", "fix": "1213"}, {"desc": "1214", "fix": "1215"}, {"desc": "1216", "fix": "1217"}, {"desc": "1158", "fix": "1218"}, {"desc": "1219", "fix": "1220"}, {"desc": "1221", "fix": "1222"}, {"desc": "1223", "fix": "1224"}, {"desc": "1225", "fix": "1226"}, {"desc": "1227", "fix": "1228"}, {"desc": "1158", "fix": "1229"}, "Update the dependencies array to be: [hostRoomId]", {"range": "1230", "text": "1231"}, "Update the dependencies array to be: [location.pathname, requireAccessToken, requireHost, roomId]", {"range": "1232", "text": "1233"}, "Update the dependencies array to be: [roomId, testName]", {"range": "1234", "text": "1235"}, "Update the dependencies array to be: [roomId, sounds]", {"range": "1236", "text": "1237"}, "Update the dependencies array to be: [isSpectator, navigate, roomId, round, setInitialGrid]", {"range": "1238", "text": "1239"}, {"range": "1240", "text": "1239"}, {"range": "1241", "text": "1239"}, {"range": "1242", "text": "1239"}, "Update the dependencies array to be: [images.length]", {"range": "1243", "text": "1244"}, {"range": "1245", "text": "1239"}, {"range": "1246", "text": "1235"}, "Update the dependencies array to be: [playerAnswerRef]", {"range": "1247", "text": "1248"}, "Update the dependencies array to be: [roomId, startTimer]", {"range": "1249", "text": "1250"}, "Update the dependencies array to be: [currentPlayerAvatar, currentPlayerName, isHost, isSpectator, playerAnswerRef, playerAnswerTime, position, roomId, setAnimationKey, timeLeft]", {"range": "1251", "text": "1252"}, "Update the dependencies array to be: [currentAnswer, isHost, roomId, setAnswerList]", {"range": "1253", "text": "1254"}, {"range": "1255", "text": "1237"}, {"range": "1256", "text": "1237"}, {"range": "1257", "text": "1252"}, {"range": "1258", "text": "1250"}, {"range": "1259", "text": "1237"}, {"range": "1260", "text": "1237"}, "Update the dependencies array to be: [hintWordArray, obstacleWord, initialGrid, isHost, setInitialGrid]", {"range": "1261", "text": "1262"}, "Update the dependencies array to be: [roomId, grid, revealCellsForPlayer]", {"range": "1263", "text": "1264"}, "Update the dependencies array to be: [roomId, setAnswerList]", {"range": "1265", "text": "1266"}, "Update the dependencies array to be: [roomId, grid, setAnswerList, revealCellsForPlayer]", {"range": "1267", "text": "1268"}, "Update the dependencies array to be: [roomId, grid, sounds, revealCellsForPlayer]", {"range": "1269", "text": "1270"}, {"range": "1271", "text": "1270"}, {"range": "1272", "text": "1237"}, "Update the dependencies array to be: [roomId]", {"range": "1273", "text": "1274"}, "Update the dependencies array to be: [roomId, isHost, setCurrentQuestion, setSelectedTopic]", {"range": "1275", "text": "1276"}, {"range": "1277", "text": "1250"}, "Update the dependencies array to be: [setAnimationKey, timeLeft]", {"range": "1278", "text": "1279"}, "Update the dependencies array to be: [isHost, roomId, testName]", {"range": "1280", "text": "1281"}, {"range": "1282", "text": "1274"}, "Update the dependencies array to be: [isHost, roomId, setSelectedTopic]", {"range": "1283", "text": "1284"}, "Update the dependencies array to be: [currentAnswer, isHost, roomId, setAnswerList, setCurrentQuestion]", {"range": "1285", "text": "1286"}, {"range": "1287", "text": "1248"}, {"range": "1288", "text": "1250"}, {"range": "1289", "text": "1252"}, {"range": "1290", "text": "1254"}, {"range": "1291", "text": "1237"}, {"range": "1292", "text": "1237"}, {"range": "1293", "text": "1279"}, {"range": "1294", "text": "1250"}, {"range": "1295", "text": "1248"}, {"range": "1296", "text": "1250"}, {"range": "1297", "text": "1252"}, {"range": "1298", "text": "1254"}, {"range": "1299", "text": "1237"}, {"range": "1300", "text": "1237"}, "Update the dependencies array to be: [initialGrid, setInitialGrid]", {"range": "1301", "text": "1302"}, {"range": "1303", "text": "1279"}, {"range": "1304", "text": "1250"}, "Update the dependencies array to be: [roomId, setBuzzedPlayer, setShowModal, sounds]", {"range": "1305", "text": "1306"}, "Update the dependencies array to be: [roomId, setShowModal, setStaredPlayer, sounds]", {"range": "1307", "text": "1308"}, {"range": "1309", "text": "1237"}, "Update the dependencies array to be: [roomId, setCorrectAnswer, sounds]", {"range": "1310", "text": "1311"}, "Update the dependencies array to be: [roomId, setCorrectAnswer, setCurrentQuestion]", {"range": "1312", "text": "1313"}, "Update the dependencies array to be: [roomId, setGridColors, setSelectedCell]", {"range": "1314", "text": "1315"}, "Update the dependencies array to be: [colorMap, roomId, setGrid, setGridColors]", {"range": "1316", "text": "1317"}, "Update the dependencies array to be: [playerAnswerRef, question]", {"range": "1318", "text": "1319"}, {"range": "1320", "text": "1274"}, "Update the dependencies array to be: [roomId, setRoomRules]", {"range": "1321", "text": "1322"}, "Update the dependencies array to be: [roomId, setPlayerArray, setPlayerScores, setScoreList]", {"range": "1323", "text": "1324"}, {"range": "1325", "text": "1266"}, "Update the dependencies array to be: [roomId, round, sounds]", {"range": "1326", "text": "1327"}, "Update the dependencies array to be: [currentRound, roomId, setInGameQuestionIndex]", {"range": "1328", "text": "1329"}, "Update the dependencies array to be: [roomId, round]", {"range": "1330", "text": "1331"}, "Update the dependencies array to be: [round, setPlayerScores]", {"range": "1332", "text": "1333"}, "Update the dependencies array to be: [round, roomId, setPlayerScores]", {"range": "1334", "text": "1335"}, {"range": "1336", "text": "1266"}, [3417, 3419], "[hostRoomId]", [5323, 5375], "[location.pathname, requireAccessToken, requireHost, roomId]", [1936, 1938], "[roomId, testName]", [995, 997], "[roomId, sounds]", [2208, 2216], "[isSpectator, navigate, roomId, round, setInitialGrid]", [2436, 2444], [2276, 2284], [3055, 3063], [519, 521], "[images.length]", [2257, 2265], [1146, 1148], [1875, 1900], "[playerAnswerRef]", [2622, 2624], "[roomId, startTimer]", [3507, 3517], "[currentP<PERSON><PERSON><PERSON><PERSON>, currentPlayer<PERSON><PERSON>, isHost, isSpectator, playerAnswerRef, playerAnswerTime, position, roomId, setAnimationKey, timeLeft]", [4232, 4234], "[current<PERSON>ns<PERSON>, isHost, roomId, setAnswerList]", [4720, 4722], [5167, 5169], [4341, 4351], [4802, 4804], [7254, 7262], [7684, 7686], [10089, 10131], "[hint<PERSON>ordArray, obstacle<PERSON>ord, initialGrid, isHost, setInitialGrid]", [22416, 22430], "[roomId, grid, revealCellsForPlayer]", [22788, 22790], "[roomId, setAnswerList]", [23836, 23850], "[roomId, grid, setAnswerList, revealCellsForPlayer]", [25089, 25103], "[roomId, grid, sounds, revealCellsForPlayer]", [26651, 26665], [6173, 6175], [6699, 6701], "[roomId]", [7869, 7885], "[roomId, isHost, setCurrentQuestion, setSelectedTopic]", [8798, 8800], [9659, 9669], "[setAnimationKey, timeLeft]", [9905, 9907], "[isHost, roomId, testName]", [10225, 10227], [10814, 10816], "[isHost, roomId, setSelectedTopic]", [12101, 12103], "[current<PERSON><PERSON><PERSON>, isHost, roomId, setAnswerList, setCurrentQuestion]", [1770, 1795], [2478, 2480], [3327, 3337], [4054, 4056], [4542, 4544], [4989, 4991], [3367, 3377], [3869, 3871], [1768, 1793], [2476, 2478], [3325, 3335], [4052, 4054], [4540, 4542], [4987, 4989], [12138, 12140], "[initialGrid, setInitialGrid]", [12757, 12767], [1852, 1854], [2759, 2767], "[roomId, setBuzzedPlayer, setShowModal, sounds]", [3683, 3691], "[roomId, setShowModal, setStaredPlayer, sounds]", [4177, 4179], [4810, 4812], "[roomId, setCorrectAnswer, sounds]", [5136, 5138], "[roomId, setCorrectAnswer, setCurrentQuestion]", [7006, 7008], "[roomId, setGridColors, setSelectedCell]", [7959, 7961], "[colorMap, roomId, setGrid, setGridColors]", [1498, 1508], "[player<PERSON><PERSON><PERSON><PERSON><PERSON>, question]", [5691, 5693], [6255, 6263], "[roomId, setRoomRules]", [8892, 8894], "[roomId, setPlayerArray, setPlayerScores, setScoreList]", [2359, 2367], [3121, 3136], "[roomId, round, sounds]", [4512, 4526], "[currentRound, roomId, setInGameQuestionIndex]", [2609, 2616], "[roomId, round]", [2827, 2834], "[round, setPlayerScores]", [3162, 3177], "[round, roomId, setPlayerScores]", [3624, 3632]]