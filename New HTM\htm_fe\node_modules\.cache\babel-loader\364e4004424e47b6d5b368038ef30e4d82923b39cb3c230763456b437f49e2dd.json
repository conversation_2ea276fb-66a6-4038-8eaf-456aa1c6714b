{"ast": null, "code": "var _s = $RefreshSig$();\n// Redux store configuration\nimport { configureStore } from '@reduxjs/toolkit';\nimport { useDispatch, useSelector } from 'react-redux';\n\n// Import slices (will be created in next steps)\nimport authSlice from './slices/authSlice';\nimport gameSlice from './slices/gameSlice';\nimport roomSlice from './slices/roomSlice';\nimport uiSlice from './slices/uiSlice';\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n    game: gameSlice,\n    room: roomSlice,\n    ui: uiSlice\n  },\n  middleware: getDefaultMiddleware => getDefaultMiddleware({\n    serializableCheck: {\n      ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE']\n    }\n  }),\n  devTools: process.env.NODE_ENV !== 'production'\n});\n// Typed hooks for better TypeScript support\nexport const useAppDispatch = () => {\n  _s();\n  return useDispatch();\n};\n_s(useAppDispatch, \"jI3HA1r1Cumjdbu14H7G+TUj798=\", false, function () {\n  return [useDispatch];\n});\nexport const useAppSelector = useSelector;", "map": {"version": 3, "names": ["configureStore", "useDispatch", "useSelector", "authSlice", "gameSlice", "roomSlice", "uiSlice", "store", "reducer", "auth", "game", "room", "ui", "middleware", "getDefaultMiddleware", "serializableCheck", "ignoredActions", "devTools", "process", "env", "NODE_ENV", "useAppDispatch", "_s", "useAppSelector"], "sources": ["C:/Users/<USER>/Documents/New HTM/htm_fe/src/app/store/index.ts"], "sourcesContent": ["// Redux store configuration\nimport { configureStore } from '@reduxjs/toolkit';\nimport { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';\n\n// Import slices (will be created in next steps)\nimport authSlice from './slices/authSlice';\nimport gameSlice from './slices/gameSlice';\nimport roomSlice from './slices/roomSlice';\nimport uiSlice from './slices/uiSlice';\n\nexport const store = configureStore({\n  reducer: {\n    auth: authSlice,\n    game: gameSlice,\n    room: roomSlice,\n    ui: uiSlice,\n  },\n  middleware: (getDefaultMiddleware) =>\n    getDefaultMiddleware({\n      serializableCheck: {\n        ignoredActions: ['persist/PERSIST', 'persist/REHYDRATE'],\n      },\n    }),\n  devTools: process.env.NODE_ENV !== 'production',\n});\n\nexport type RootState = ReturnType<typeof store.getState>;\nexport type AppDispatch = typeof store.dispatch;\n\n// Typed hooks for better TypeScript support\nexport const useAppDispatch = () => useDispatch<AppDispatch>();\nexport const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;\n"], "mappings": ";AAAA;AACA,SAASA,cAAc,QAAQ,kBAAkB;AACjD,SAA+BC,WAAW,EAAEC,WAAW,QAAQ,aAAa;;AAE5E;AACA,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,SAAS,MAAM,oBAAoB;AAC1C,OAAOC,OAAO,MAAM,kBAAkB;AAEtC,OAAO,MAAMC,KAAK,GAAGP,cAAc,CAAC;EAClCQ,OAAO,EAAE;IACPC,IAAI,EAAEN,SAAS;IACfO,IAAI,EAAEN,SAAS;IACfO,IAAI,EAAEN,SAAS;IACfO,EAAE,EAAEN;EACN,CAAC;EACDO,UAAU,EAAGC,oBAAoB,IAC/BA,oBAAoB,CAAC;IACnBC,iBAAiB,EAAE;MACjBC,cAAc,EAAE,CAAC,iBAAiB,EAAE,mBAAmB;IACzD;EACF,CAAC,CAAC;EACJC,QAAQ,EAAEC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK;AACrC,CAAC,CAAC;AAKF;AACA,OAAO,MAAMC,cAAc,GAAGA,CAAA;EAAAC,EAAA;EAAA,OAAMrB,WAAW,CAAc,CAAC;AAAA;AAACqB,EAAA,CAAlDD,cAAc;EAAA,QAASpB,WAAW;AAAA;AAC/C,OAAO,MAAMsB,cAA+C,GAAGrB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}