<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exam Management Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
        }
        .card-header {
            font-weight: bold;
        }
        table thead th {
            background-color: #343a40;
            color: #fff;
        }
        .btn-custom {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-4">Exam Management Dashboard</h1>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="dashboardTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="upload-tab" data-bs-toggle="tab" data-bs-target="#upload" type="button" role="tab" aria-controls="upload" aria-selected="true">
                    Upload Exam
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="view-tab" data-bs-toggle="tab" data-bs-target="#view" type="button" role="tab" aria-controls="view" aria-selected="false">
                    View Exams
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="setup-tab" data-bs-toggle="tab" data-bs-target="#setup" type="button" role="tab" aria-controls="setup" aria-selected="false">
                    Setup Match
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="dashboardTabsContent">
            <!-- Upload Exam Tab -->
            <div class="tab-pane fade show active" id="upload" role="tabpanel" aria-labelledby="upload-tab">
                <div class="card mt-4">
                    <div class="card-header">Upload Exam File</div>
                    <div class="card-body">
                        <form action="/upload-excel" method="POST" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label for="fileUpload" class="form-label">Upload Excel File</label>
                                <input class="form-control" type="file" id="fileUpload" name="excelFile" required>
                            </div>
                            <div class="mb-3">
                                <a href="/example-template" class="btn btn-outline-secondary">Download Example Template</a>
                            </div>
                            <button type="submit" class="btn btn-custom">Upload</button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- View Exams Tab -->
            <div class="tab-pane fade" id="view" role="tabpanel" aria-labelledby="view-tab">
                <div class="card mt-4">
                    <div class="card-header">Uploaded Exams</div>
                    <div class="card-body">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>Exam Name</th>
                                    <th>Uploaded Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>Math Exam</td>
                                    <td>2025-03-15</td>
                                    <td>
                                        <a href="/edit-exam/1" class="btn btn-warning btn-sm">Edit</a>
                                        <a href="/delete-exam/1" class="btn btn-danger btn-sm">Delete</a>
                                    </td>
                                </tr>
                                <!-- More rows can be dynamically inserted here -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- Setup Match Tab -->
            <div class="tab-pane fade" id="setup" role="tabpanel" aria-labelledby="setup-tab">
                <div class="card mt-4">
                    <div class="card-header">Setup Match</div>
                    <div class="card-body">
                        <form action="/update-match" method="POST">
                            <div class="mb-3">
                                <label for="currentRound" class="form-label">Current Round</label>
                                <input type="text" class="form-control" id="currentRound" name="currentRound" required>
                            </div>
                            <button type="submit" class="btn btn-primary">Update Round</button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
