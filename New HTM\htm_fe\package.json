{"name": "htm_fe", "version": "0.1.0", "private": true, "dependencies": {"@heroicons/react": "^2.2.0", "@reduxjs/toolkit": "^2.8.2", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.2.0", "@testing-library/user-event": "^13.5.0", "@types/react-router-dom": "^5.3.3", "clsx": "^2.1.1", "firebase": "^11.4.0", "jwt-decode": "^4.0.0", "lucide": "^0.511.0", "lucide-react": "^0.511.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-placeholder": "^4.1.0", "react-query": "^3.39.3", "react-redux": "^9.2.0", "react-router-dom": "^7.3.0", "react-scripts": "5.0.1", "react-signify": "^1.5.6", "react-toastify": "^11.0.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "ajv": "^7.2.4", "autoprefixer": "^10.4.20", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.8.2"}}